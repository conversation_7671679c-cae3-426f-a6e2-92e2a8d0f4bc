<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--flat !h-auto">
    <template #before> </template>

    <template #toolbar:after="{ selected, selectionIds, selection }">
      <!-- 添加一键分配按钮 -->
      <el-button
        v-hasPermi="['risk:warn:oneClickAssign']"
        type="success"
        :disabled="!selected"
        @click="onOneClickAssignClick(selectionIds)"
      ><i class="el-icon-thumb" />一键分配</el-button>

      <el-button
        v-hasPermi="['risk:middle:app:loophole:dispose']"
        type="primary"
        :disabled="!selected"
        @click="onDisposeClick(selectionIds, selection)"
      ><IconHandyman class="" />处置</el-button>

      <!-- 添加审核按钮 -->
      <!-- <el-button
        v-hasPermi="['risk:warn:disposalAudit']"
        type="danger"
        :disabled="!selected"
        @click="onDisposalAuditClick(selectionIds)"
      ><i class="el-icon-check" />审核</el-button> -->

      <el-button
        v-if="$checkPermi(['risk:middle:app:loophole:assign'])"
        type="warning"
        icon="iconfont icon-piliangfenpei1"
        :disabled="!selected"
        @click="onAssignClick(selectionIds)"
      >分配</el-button>

    </template>

    <template #table:action:after="{ row }">
      <el-button v-hasPermi="['risk:warn:oneClickAssign']" type="text" size="mini" @click="onOneClickAssignClick([row.id])">一键分配</el-button>
      <el-button v-if="$checkPermi(['risk:middle:app:loophole:dispose'])" type="text" size="mini" @click="onDisposeClick([row.id], [row])">处置</el-button>
      <!-- <el-button type="text" size="mini" @click="onDisposalAuditClick([row.id])">审核</el-button> -->
    </template>

    <template #after>
      <AssignDialog ref="assignDialogRef" />
      <DisposeDialog ref="disposeDialogRef" />
      <DisposalAuditDialog ref="disposalAuditDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import {
  addAppTestDetail,
  delAppTestDetail,
  getAppTestDetail,
  listAppTestDetail,
  updateAppTestDetail
} from '@/api/risk/resource/appDetail.js'
import { riskDisposeOneClickAssign } from '@/api/risk/basic/index.js'
import DisposeDialog from '@/views/risk/basic/components/DisposeDialog/index.vue'
import AssignDialog from '@/views/risk/basic/components/AssignDialog/index.vue'
import DisposalAuditDialog from '@/views/risk/basic/components/DisposalAuditDialog/index.vue'

import { middleAppTestType } from '@/dicts/basic/index.js'

import request from '@/utils/request.js'

export default {
  components: {
    DisposeDialog,
    AssignDialog,
    DisposalAuditDialog
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  dicts: ['problem_severity_level', 'allocation_status', 'dispose_status_app_detection', 'loophole_risk_level', 'dispose_review_status'],
  data() {
    return {
      riskType: middleAppTestType
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: 'APP安全检测明细',

        lazy: false,

        api: {
          // add: (params) => addAppTestDetail({ ...params }),
          // edit: (params) => updateAppTestDetail({ ...params }),
          info: getAppTestDetail,
          list: async(params) => listAppTestDetail({ ...params }),
          remove: delAppTestDetail,
          export: '/risk/app/detail/export',
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          export: !this.$checkPermi(['risk:middle:app:loophole:export']),
          remove: !this.$checkPermi(['risk:middle:app:loophole:remove'])
        },

        flowProps: {
          preset: 'disposal',
          params: {
            riskType: this.riskType
          }
        },

        model: {
          taskId: {
            hidden: true,
            search: {
              value: this.params.taskId
            }
          },
          detectionItemId: {
            label: 'CVE编号',
            table: {
              width: 200,
              align: 'left'
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          typeId: {
            label: '漏洞类型',
            table: {
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          detectionItemName: {
            label: '漏洞标题',
            table: {
              width: 200
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            }
          },
          grade: {
            type: 'select',
            label: '风险等级',
            table: {
              width: 90
            },
            search: {
              hidden: false
            },
            form: {
              rules: true
            },
            options: this.dict.type.loophole_risk_level
          },
          status: {
            label: '影响分数',
            search: {
              hidden: true
            },
            table: {
              width: 100
            }
          },
          overTime: {
            type: 'text',
            label: '发布时间',
            table: {
              width: 170
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          purpose: {
            label: '漏洞的排查方式',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          harm: {
            label: '漏洞参考链接名称',
            table: {
              width: 150
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          solution: {
            label: '漏洞参考URL',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          resultContent: {
            type: 'text',
            label: '补丁',
            table: {
              width: 90
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },

          describe: {
            type: 'text',
            label: '漏洞描述',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },

          ruleNo: {
            label: 'CVSS 评分',
            search: {
              hidden: true
            }
          },

          disposeStatus: {
            label: '处置状态',
            type: 'select',
            options: this.dict.type.dispose_status_app_detection
          },
          dispatchStatus: {
            label: '分配状态',
            type: 'select',
            group: 'flow',
            options: this.dict.type.allocation_status
          },
          riskOperatorGroupName: {
            label: '处置分组',
            group: 'flow'
          },
          // 添加审核状态字段
          // reviewStatus: {
          //   label: '审核状态',
          //   type: 'select',
          //   options: this.dict.type.dispose_review_status,
          //   search: {
          //     hidden: false
          //   },
          //   table: {
          //     width: 100
          //   }
          // }
        }
      }

      return value
    }
  },
  methods: {
    getTableData() {
      this.$refs.sheetRef.getTableData()
    },
    onAssignClick(ids, args = {}) {
      const params = {
        ids,
        riskType: this.riskType,
        groupKey: 'basic_type_middle_dim',
        disposeType: 'APP安全检测'
      }

      this.$refs.assignDialogRef.open({
        ...args,
        params,
        success: () => {
          this.getTableData()
        }
      })
    },
    onDisposeClick(ids, selection) {
      let params = {}

      if (Array.isArray(ids)) {
        params = {
          ids,
          selection
        }
      } else {
        params = {
          id: ids[0]
        }
      }

      Object.assign(params, {
        riskType: this.riskType
      })

      this.$refs.disposeDialogRef.open({
        params,
        success: () => {
          this.getTableData()
        }
      })
    },
    onDisposalAuditClick(ids) {
      if (ids.length === 0) {
        this.$message.warning('请选择需要处置审核的数据')
        return
      }

      this.$refs.disposalAuditDialogRef.open({
        params: {
          riskType: this.riskType,
          ids
        },
        success: () => {
          this.getTableData()
        }
      })
    },
    // 添加一键分配方法
    async onOneClickAssignClick(ids) {
      if (ids.length === 0) {
        this.$message.warning('请选择需要一键分配的数据')
        return
      }

      try {
        await this.$confirm('确定要执行一键分配吗?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return
      }

      // 获取组件的查询条件参数
      let queryObj = {}
      if (this.$refs.sheetRef) {
        queryObj = { ...this.$refs.sheetRef.listParameter() }
      }

      // 添加任务ID参数
      if (this.params && this.params.taskId) {
        queryObj.taskId = this.params.taskId
      }

      const res = await riskDisposeOneClickAssign({
        id: ids.join(','),
        riskType: this.riskType,
        queryObj
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.getTableData()
      }
    }
  }
}
</script>

<style></style>
