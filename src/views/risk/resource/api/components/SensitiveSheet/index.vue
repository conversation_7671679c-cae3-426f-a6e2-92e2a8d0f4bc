<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main" :class="autoHeight ? 'h-full !overflow-hidden' :''" v-on="$listeners">
    <template #search:timeType="{ model }">
      <el-form-item label="时间类型" class="!w-142">
        <div class="flex items-center">
          <el-select v-model="model.timeType" placeholder="请选择" class="flex-none !w-36" clearable filterable>
            <el-option
              label="最近发现时间"
              value="0"
            >
            </el-option>
            <el-option
              label="入库时间"
              value="1"
            >
            </el-option>
          </el-select>

          <EleDatePickerRange
            class="flex-1 w-0"
            :disabled="!model.timeType"
            :start-value.sync="model.startTime"
            :end-value.sync="model.endTime"
            :picker-options="pickerOptions"
          />
        </div>

      </el-form-item>
    </template>

    <template #search:riskOperatorGroupId:simple="{ model }">
      <DisposeGroupSelect
        ref="disposeGroupSelectRef"
        v-model="model.riskOperatorGroupId"
        placeholder="选择处置分组"
        clearable
        :params="{
          groupKey: 'basic_type_middle_dim',
          disposeType: 'API安全网关敏感数据',
        }"
      />
    </template>

    <template #toolbar:after="scope">
      <el-button
        v-hasPermi="['risk:warn:oneClickAssign']"
        type="success"
        :disabled="!scope.selected"
        @click="onOneClickAssignClick(scope.selectionIds)"
      ><i class="el-icon-thumb" />一键分配</el-button>
      <el-button
        v-hasPermi="['risk:warn:api:dispose']"
        type="primary"
        :disabled="!scope.selected"
        @click="handleDispose(scope)"
      ><IconHandyman class="" />处置</el-button>

      <AllocationActions
        v-if="$checkPermi(['risk:middle:api:sensitive:assign'])"
        v-bind="{
          multiple: !scope.selected,
          ids: scope.selectionIds,
          getList: () => $refs.sheetRef.getTableData(),
          riskType: '20',
          disposeType: 'API安全网关敏感数据',
        }"
      />
    </template>

    <template #table:action:after="{ row }">
      <el-button v-hasPermi="['risk:warn:oneClickAssign']" type="text" size="mini" @click="onOneClickAssignClick([row.id])">一键分配</el-button>
      <el-button
        v-if="row.disposeStatus != 1"
        v-hasPermi="['risk:warn:api:dispose']"
        size="mini"
        type="text"
        @click="handleDispose(row)"
      >处置</el-button>
    </template>

    <template #table:apiId:simple="{ row }">
      <sa-tooltip :content="row.apiId" is-copy />
    </template>
    <template #table:piiLevel:simple="{ row }">
      <div v-if="row.piiLevel ==='低'" class="risk-low-cor">{{ row.piiLevel }}</div>
      <div v-if="row.piiLevel === '中'" class="risk-centre-cor">{{ row.piiLevel }}</div>
      <div v-if="row.piiLevel === '高'" class="risk-high-cor">{{ row.piiLevel }}</div>
    </template>
    <template #table:endpoint:simple="{ row }">
      <div class="endpoint-container">
        <sa-tooltip :content="row.endpoint" is-copy />
        <span class="endpoint-type">{{ row.method }}</span>
      </div>
      <span class="dot"></span>
    </template>

    <!-- <template #info:after="{ data }">
      <TaskFlowProfile
        v-if="data.id"
        v-bind="{
          taskInfo: data,
          params: {
            riskType: '8',
            id: data.id,
          },
          disposeResultDict: dict.type.pii_status,
          disposeResultKey: 'disposeStatus',
          disposeRemarkKey: 'remarks',
        }"
      />
    </template> -->

    <template #after>
      <DisposeDialog ref="disposeDialogRef" />
    </template>
  </EleSheet>
</template>

<script>

import request from '@/utils/request'

import DisposeDialog from './DisposeDialog/index.vue'
import TaskFlowProfile from '@/components/business/TaskFlowProfile/index.vue'

import disabledDate from '@/utils/disabledDate.js'

import AllocationActions from '@/views/risk/resource/components/AllocationActions/index.vue'

import { riskDisposeOneClickAssign } from '@/api/risk/basic/index.js'

// 查询API安全网关列表
function listApi(query) {
  return request({
    url: '/risk/piis/list',
    method: 'get',
    params: query
  })
}

export default {
  dicts: ['pii_status', 'allocation_status'],
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  components: {
    DisposeDialog,
    TaskFlowProfile,
    AllocationActions
  },
  data() {
    return {
      pickerOptions: {
        disabledDate
      }
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: 'API网关-敏感信息',

        lazy: false,

        api: {
          list: async(params) => listApi({ ...params, ...this.params }),
          info: async(id, params) => {
            const res = await listApi({ apiId: params.apiId })
            return {
              code: 200,
              data: res?.rows?.[0] || {}
            }
          },
          export: '/risk/piis/export',
          import: '',
          template: ''
          // add: (params) => addApi({ ...params }),
          // edit: (params) => updateApi({ ...params }),
          // remove: delApi,
        },

        tableProps: {
          selection: 'multiple',
          ...(this.autoHeight ? {
            height: '100%'
          } : {})
        },

        infoProps: {
          title: true
        },

        hiddenActions: [!this.$checkPermi(['risk:api:group:export']) && 'export'],

        model: {
          apiId: {
            label: 'ApiId',
            type: 'text',
            width: 200,
            align: 'left'
          },
          endpoint: {
            label: 'API路径',
            type: 'text',
            width: 250,
            align: 'left'
          },
          piiLevel: {
            label: '风险等级',
            type: 'text',
            width: 100
          },
          appSiteName: {
            label: '应用',
            type: 'text',
            width: 150,
            align: 'left',
            search: {
              sort: 51
            }
          },
          piiType: {
            label: '敏感类型',
            type: 'text',
            align: 'left',
            width: 150
          },
          disposeStatus: {
            type: 'select',
            label: '处置状态',
            options: this.dict.type.pii_status
          },
          recentDetectedTime: {
            label: '最近发现时间',
            type: 'text',
            width: 180,
            search: {
              hidden: true
            }
          },
          dispatchStatus: {
            type: 'select',
            label: '分配状态',
            options: this.dict.type.allocation_status
          },
          riskOperatorGroupId: {
            label: '处置分组',
            formatter: (row) => row.riskOperatorGroupName
          },
          createTime: {
            label: '入库时间',
            type: 'text',
            width: 200,
            search: {
              hidden: true
            }
          },
          timeType: {
            type: 'text',
            label: '时间类型',
            search: {
              hidden: false
            },
            hidden: true
          }
        }
      }

      return value
    }
  },
  methods: {
    async onOneClickAssignClick(ids) {
      if (ids.length === 0) {
        this.$message.warning('请选择需要一键分配的数据')
        return
      }

      try {
        await this.$confirm('确定要执行一键分配吗?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return
      }

      // 获取组件的查询条件参数
      let queryObj = {}
      if (this.$refs.sheetRef) {
        queryObj = { ...this.$refs.sheetRef.listParameter() }
      }

      const res = await riskDisposeOneClickAssign({
        id: ids.join(','),
        riskType: '20',
        queryObj
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    handleDispose(event) {
      const params = ['multiple'].includes(event.selectionType)
        ? {
          ids: event.selectionIds,
          selection: event.selection
        }
        : {
          id: event.id
        }

      Object.assign(params, {
        riskType: 'api-sensitive'
      })

      this.$refs.disposeDialogRef.open({
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    }
  }
}
</script>

<style>
  .endpoint-container {
    display: flex;
    justify-content: space-between; /* 这会使得内容分布在两端 */
    align-items: center;     /* 垂直居中 */
    width: 100%; /* 确保容器占满整个单元格宽度 */
  }

  .endpoint-type {
    white-space: nowrap; /* 防止文本换行 */
    background-color: #f0f2f7;
    border-radius: 7px;
    padding: 2px 4px;
  }

  .dot {
    display: inline-block;
    width: 6px; /* 小圆点的宽度 */
    height: 6px; /* 小圆点的高度 */
    background-color: #43c1a3; /* 背景颜色 */
    border-radius: 50%; /* 使小圆点成为圆形 */
    position: absolute;
    top: 45%;
    left: 98%;
  }
  .risk-low-cor{
    color: #a164d9;
  }
  .risk-centre-cor{
    color: #e46e29
  }
  .risk-high-cor{
    color: #d4010e;
  }

</style>
