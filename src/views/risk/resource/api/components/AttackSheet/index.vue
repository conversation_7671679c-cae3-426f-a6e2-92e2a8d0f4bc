<template>
  <EleSheet
    ref="sheetRef"
    v-bind="sheetProps"
    class="page-main"
    :class="autoHeight ? 'h-full !overflow-hidden' : ''"
    v-on="$listeners"
  >
    <template #search:timeType="{ model }">
      <el-form-item label="时间类型" class="!w-142">
        <div class="flex items-center">
          <el-select
            v-model="model.timeType"
            placeholder="请选择"
            class="flex-none !w-36"
            clearable
            filterable
          >
            <el-option label="攻击开始时间" value="0"> </el-option>
            <el-option label="入库时间" value="1"> </el-option>
          </el-select>

          <EleDatePickerRange
            class="flex-1 w-0"
            :disabled="!model.timeType"
            :start-value.sync="model.startTime"
            :end-value.sync="model.endTime"
            :picker-options="pickerOptions"
          />
        </div>
      </el-form-item>
    </template>

    <template #search:riskOperatorGroupId:simple="{ model }">
      <DisposeGroupSelect
        ref="disposeGroupSelectRef"
        v-model="model.riskOperatorGroupId"
        placeholder="选择处置分组"
        clearable
        :params="{
          groupKey: 'basic_type_middle_dim',
          disposeType: 'API安全网关攻击',
        }"
      />
    </template>

    <template #table:riskLevel:simple="{ row }">
      <div v-if="row.riskLevel === '低'" class="risk-low-cor">{{ row.riskLevel }}</div>
      <div v-if="row.riskLevel === '中'" class="risk-centre-cor">{{ row.riskLevel }}</div>
      <div v-if="row.riskLevel === '高'" class="risk-high-cor">{{ row.riskLevel }}</div>
    </template>
    <template #table:attackSource:simple="{ row }">
      <div class="attack-source-container">
        <sa-tooltip :content="row.attackSource" is-copy />
        <span class="attack-source-type">{{ row.attackSourceType }}</span>
      </div>
    </template>
    <template #table:relatedApi:simple="{ row }">
      <sa-tooltip :content="formatRelatedApi(row.relatedApi)" is-copy />
    </template>

    <template #toolbar:after="scope">
      <el-button
        v-hasPermi="['risk:warn:oneClickAssign']"
        type="success"
        :disabled="!scope.selected"
        @click="onOneClickAssignClick(scope.selectionIds)"
      ><i class="el-icon-thumb" />一键分配</el-button>
      <!-- <el-button
        v-hasPermi="['risk:warn:api:dispose']"
        type="primary"
        :disabled="!scope.selected"
        @click="handleDispose(scope)"
      ><IconHandyman class="" />处置</el-button> -->

      <AllocationActions
        v-if="$checkPermi(['risk:middle:api:attack:assign'])"
        v-bind="{
          multiple: !scope.selected,
          ids: scope.selectionIds,
          getList: () => $refs.sheetRef.getTableData(),
          riskType: '19',
          disposeType: 'API安全网关攻击',
        }"
      />
    </template>

    <template #table:action:after="{ row }">
      <el-button v-hasPermi="['risk:warn:oneClickAssign']" type="text" size="mini" @click="onOneClickAssignClick([row.id])">一键分配</el-button>

      <!-- <el-button
        v-if="row.attackStatus != 2"
        v-hasPermi="['risk:warn:api:dispose']"
        size="mini"
        type="text"
        @click="handleDispose(row)"
      >处置</el-button> -->
    </template>

    <!-- <template #info:after="{ data }">
      <TaskFlowProfile
        v-if="data.id"
        v-bind="{
          taskInfo: data,
          params: {
            riskType: '8',
            id: data.id,
          },
          disposeResultDict: dict.type.attack_status,
          disposeResultKey: 'attackStatus',
          disposeRemarkKey: 'remarks',
        }"
      />
    </template> -->

    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request'
import TaskFlowProfile from '@/components/business/TaskFlowProfile/index.vue'
import disabledDate from '@/utils/disabledDate.js'
import AllocationActions from '@/views/risk/resource/components/AllocationActions/index.vue'
import { riskDisposeOneClickAssign } from '@/api/risk/basic/index.js'

// 查询API安全网关列表
function listApi(query) {
  return request({
    url: '/risk/attacks/list',
    method: 'get',
    params: query
  })
}

export default {
  dicts: ['attack_status', 'allocation_status'],
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  components: {
    TaskFlowProfile,
    AllocationActions
  },
  data() {
    return {
      pickerOptions: {
        disabledDate
      }
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: 'API网关-攻击',

        lazy: false,

        api: {
          list: async(params) => listApi({ ...params, ...this.params }),
          info: async(id, params) => {
            const res = await listApi({ apiId: params.apiId })
            return {
              code: 200,
              data: res?.rows?.[0] || {}
            }
          },
          export: '/risk/attacks/export',
          import: '',
          template: ''
          // add: (params) => addApi({ ...params }),
          // edit: (params) => updateApi({ ...params }),
          // remove: delApi,
        },

        tableProps: {
          selection: 'multiple',
          ...(this.autoHeight
            ? {
              height: '100%'
            }
            : {})
        },

        infoProps: {
          title: true
        },

        hiddenActions: [!this.$checkPermi(['risk:api:group:export']) && 'export'],

        model: {
          startTime: {
            label: '攻击开始时间',
            type: 'date',
            width: 180,
            search: {
              hidden: true
            }
          },
          riskLevel: {
            label: '风险等级',
            type: 'text',
            width: 80
          },
          eventCategory: {
            label: '攻击类型',
            type: 'text',
            width: 120,
            align: 'left'
          },
          attackName: {
            label: '攻击名称',
            type: 'text',
            width: 150,
            align: 'left'
          },
          attackSource: {
            label: '攻击来源',
            type: 'text',
            width: 170
          },
          relatedApi: {
            label: '攻击目标',
            type: 'text',
            width: 170,
            align: 'left',
            search: {
              hidden: true
            }
          },
          createTime: {
            type: 'text',
            label: '入库时间',
            search: {
              hidden: true
            },
            width: 180
          },
          endTime: {
            label: '攻击结束时间',
            type: 'text',
            width: 180,
            search: {
              hidden: true
            }
          },
          timeType: {
            type: 'text',
            label: '时间类型',
            search: {
              hidden: false
            },
            hidden: true
          },
          dispatchStatus: {
            type: 'select',
            label: '分配状态',
            options: this.dict.type.allocation_status
          },
          riskOperatorGroupId: {
            label: '处置分组',
            formatter: (row) => row.riskOperatorGroupName
          }
        }
      }

      return value
    }
  },
  methods: {
    async onOneClickAssignClick(ids) {
      if (ids.length === 0) {
        this.$message.warning('请选择需要一键分配的数据')
        return
      }

      try {
        await this.$confirm('确定要执行一键分配吗?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return
      }

      // 获取组件的查询条件参数
      let queryObj = {}
      if (this.$refs.sheetRef) {
        queryObj = { ...this.$refs.sheetRef.listParameter() }
      }

      const res = await riskDisposeOneClickAssign({
        id: ids.join(','),
        riskType: '19',
        queryObj
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    handleDispose(event) {
      const params = ['multiple'].includes(event.selectionType)
        ? {
          ids: event.selectionIds,
          selection: event.selection
        }
        : {
          id: event.id
        }

      Object.assign(params, {
        riskType: 8
      })

      this.$refs.disposeDialogRef.open({
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    formatRelatedApi(value) {
      try {
        const rdata = JSON.parse(value)
        if (Array.isArray(rdata)) {
          // 遍历数组并拼接 id 和 name，使用 <br> 进行换行
          const result = rdata.map((item) => `${item.app_site_name} ${item.endpoint}`).join(';')
          return result
        } else {
          return value
        }
      } catch (error) {
        console.error('Error in formatAttackSource:', error)
        return value
      }
    }
  }
}
</script>

<style>
  .attack-source-container {
    display: flex;
    justify-content: space-between; /* 这会使得内容分布在两端 */
    width: 100%; /* 确保容器占满整个单元格宽度 */
  }

  .attack-source-type {
    white-space: nowrap; /* 防止文本换行 */
    background-color: #f0f2f7;
    border-radius: 5px;
    padding: 2px 4px;
  }
  .risk-low-cor {
    color: #a164d9;
  }
  .risk-centre-cor {
    color: #e46e29;
  }
  .risk-high-cor {
    color: #d4010e;
  }
</style>
