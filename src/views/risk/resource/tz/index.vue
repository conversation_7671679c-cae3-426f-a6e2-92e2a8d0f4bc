<template>
  <div class="page-main" :class="autoHeight ? 'h-full overflow-hidden' : ''">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item label="告警事件ID" prop="warnId">
        <el-input
          v-model="queryParams.warnId"
          placeholder="请输入告警事件ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="告警类型" prop="type">
        <el-input
          v-model="queryParams.type"
          placeholder="请输入告警类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="告警描述" prop="description">
        <el-input
          v-model="queryParams.description"
          placeholder="请输入告警描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="部门名称" prop="deptName">
        <CommonDepartmentSelect
          v-model="queryParams.deptName"
          placeholder="请输入"
          clearable
          return-name
          @change="()=> queryParams.systemName = void 0"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="业务系统" prop="systemName">
        <CommonSystemSelect
          v-model="queryParams.systemName"
          placeholder="请输入"
          return-name
          clearable
          :params="{
            deptName: queryParams.deptName
          }"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="严重级别" prop="severity">
        <el-select
          v-model="queryParams.severity"
          placeholder="请选择严重级别"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处置状态" prop="status">
        <EleSelectDict
          v-model="queryParams.status"
          clearable
          dict-type="container_safety_one_dispose_status"
          remote
          placeholder="请选择处置状态"
          @change="handleQuery"
        ></EleSelectDict>
      </el-form-item>

      <el-form-item label="分配状态" prop="dispatchStatus">
        <EleSelectDict
          v-model="queryParams.dispatchStatus"
          :options="dict.type.allocation_status"
          placeholder="请选择分配状态"
          @change="handleQuery"
        ></EleSelectDict>
      </el-form-item>

      <el-form-item label="处置分组" prop="riskOperatorGroupId">
        <DisposeGroupSelect
          ref="disposeGroupSelectRef"
          v-model="queryParams.riskOperatorGroupId"
          placeholder="选择处置分组"
          clearable
          :params="{
            groupKey: 'basic_type_middle_dim',
            disposeType: '容器安全',
          }"
        />
      </el-form-item>

      <el-form-item class="create-time datetimerange" label="创建时间" prop="daterange">
        <el-date-picker
          v-model="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          class="!w-full"
          @change="onChangeDaterange"
        />
      </el-form-item>
      <el-form-item class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['risk:tz:group:export']"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >导出</el-button>
      <el-button
        v-hasPermi="['risk:tz:group:exportStats']"
        type="primary"
        icon="el-icon-download"
        @click="handleStatsExport"
      >导出统计报表</el-button>
      <el-button
        v-hasPermi="['risk:warn:oneClickAssign']"
        type="success"
        :disabled="multiple"
        @click="onOneClickAssignClick(ids)"
      ><i class="el-icon-thumb" />一键分配</el-button>

      <el-button
        v-hasPermi="['risk:warn:tz:dispose']"
        type="primary"
        :disabled="multiple"
        @click="onOper()"
      ><IconHandyman class="" />处置</el-button>
      <el-button
        v-if="$checkPermi(['risk:turnoverRecord:tz:goBackRiskData'])"
        type="warning"
        :disabled="multiple"
        @click="onMibbleSendBack"
      ><IconUndo class="el-icon" />退回</el-button>
      <el-button
        v-if="$checkPermi(['risk:turnoverRecord:tz:transferRiskData'])"
        type="warning"
        :disabled="multiple"
        @click="onMibbleReassignment"
      ><IconForward class="" />转派</el-button>
      <el-button v-if="$checkPermi(['risk:tz:group:markers'])" type="success" :disabled="multiple" @click="onMark()"><IconCheck class="icon" />标记</el-button>

      <AllocationActions
        v-if="$checkPermi(['risk:middle:tz:assign'])"
        v-bind="{
          multiple,
          ids,
          riskType: '18',
          disposeType: '容器安全',
          getList
        }"
      />

      <!-- <el-button
        type="danger"
        plain
        :disabled="multiple"
        @click="handleDelete"
        v-hasPermi="['resource:tz:remove']"
        ><i class="iconfont icon-piliangshanchu"></i>批量删除</el-button
      > -->
    </div>

    <div v-full:height="autoHeight" class="">
      <el-table
        v-loading="loading"
        class="sa-table"
        v-bind="{
          ...(autoHeight ? { height: '100%' } : {})
        }"
        :data="tzList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column
        label="告警事件ID"
        align="center"
        prop="warnId"
        min-width="210"
        :show-overflow-tooltip="true"
      /> -->
        <el-table-column
          label="告警类型"
          align="center"
          prop="type"
          min-width="210"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="告警描述"
          align="center"
          prop="description"
          min-width="210"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="部门名称"
          align="center"
          prop="deptName"
          min-width="120"
          :formatter="unknownFormat"
        />
        <el-table-column
          label="业务系统"
          align="center"
          prop="systemName"
          min-width="120"
          :formatter="unknownFormat"
        />

        <el-table-column
          label="资源IP"
          align="center"
          prop="serviceIp"
          min-width="120"
          :formatter="unknownFormat"
        />

        <el-table-column label="严重级别" align="center" width="100" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <dict-tag
              v-if="scope.row.severity"
              :options="dict.type.risk_level"
              :value="scope.row.severity"
            />
            <span v-else>未知</span>
          </template>
        </el-table-column>
        <el-table-column label="容器名称" align="center" width="100" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span v-if="scope.row.context">
              {{
                scope.row.context.container ||
                  scope.row.context['Container Name'] ||
                  scope.row.context.container_name ||
                  '-'
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="系统调用" align="center" width="100" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span v-if="scope.row.context">
              {{ scope.row.context.syscall || scope.row.context['syscall.type'] || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="进程命令行" align="center" width="100" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span v-if="scope.row.context">
              {{
                scope.row.context.command ||
                  scope.row.context.proc_cmdline ||
                  scope.row.context.proc_exeline ||
                  '-'
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="进程名" align="center" width="100" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span v-if="scope.row.context">
              {{ scope.row.context.proc_name || scope.row.context.procName || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="重复次数" align="center" width="80">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="onRepeatNum(scope.row)">
              {{ scope.row.repeatNum }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="处置状态" align="center" min-width="120">
          <template slot-scope="scope">
            <EleTagDict :value="scope.row.status" :options="dict.type.container_safety_one_dispose_status" />
          </template>
        </el-table-column>

        <el-table-column label="分配状态" align="center" min-width="120">
          <template slot-scope="scope">
            <EleTagDict :value="scope.row.dispatchStatus" :options="dict.type.allocation_status" />
          </template>
        </el-table-column>

        <el-table-column label="处置分组" prop="riskOperatorGroupName" align="center" min-width="120">
        </el-table-column>

        <el-table-column label="创建时间" align="center" prop="createTime" width="180" />
        <el-table-column fixed="right" label="操作" align="center" width="140">
          <template slot-scope="scope">
            <el-button v-hasPermi="['risk:warn:oneClickAssign']" type="text" size="mini" @click="onOneClickAssignClick([scope.row.id])">一键分配</el-button>
            <el-button
              v-if="scope.row.status != 2"
              v-hasPermi="['risk:warn:tz:dispose']"
              size="mini"
              type="text"
              @click="onOper(scope.row)"
            >处置</el-button>
            <el-button v-if="$checkPermi(['risk:tz:group:query'])" size="mini" type="text" @click="onDetail(scope.row)">查看</el-button>
            <el-dropdown @command="(command) => handleCommand(command, scope.row)">
              <el-button size="mini" type="text">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="$checkPermi(['risk:turnoverRecord:tz:goBackRiskData']) && scope.row.status != 2" command="mibbleSendBack">
                  <el-button size="mini" type="text">退回</el-button>
                </el-dropdown-item>
                <el-dropdown-item v-if="$checkPermi(['risk:turnoverRecord:tz:transferRiskData']) && scope.row.status != 2" command="mibbleReassignment">
                  <el-button size="mini" type="text">转派</el-button>
                </el-dropdown-item>
                <el-dropdown-item command="analyse">
                  <el-button
                    v-hasPermi="['risk:tz:relevance:list']"
                    size="mini"
                    type="text"
                  >分析</el-button>
                </el-dropdown-item>
                <el-dropdown-item v-if="$checkPermi(['risk:tz:group:associatedObj'])" command="resources">
                  <el-button size="mini" type="text">关联对象</el-button>
                </el-dropdown-item>
                <el-dropdown-item v-if="$checkPermi(['risk:tz:group:markers'])" command="markEnd">
                  <el-button size="mini" type="text">标记</el-button>
                </el-dropdown-item>
                <el-dropdown-item v-if="$checkPermi(['risk:tz:group:whiteList'])" command="joinWhiteList">
                  <el-button size="mini" type="text">加入白名单</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 重复次数 -->
    <repeat-num ref="repeatNumRef" :repeat-num-data="repeatNumData" />

    <!-- 分析 -->
    <analyse-detail ref="analyse" :analyse-data="analyse" />

    <!-- 处置 -->
    <OperDockfile ref="operDockfile" @ok="onOkOper" />

    <!-- 详情 -->
    <sa-detail ref="detail" :detail-data="detail" />

    <!-- 添加或修改容器安全与微隔离-探真对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="租户id" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户id" />
        </el-form-item>
        <el-form-item label="租户名称" prop="tenantName">
          <el-input v-model="form.tenantName" placeholder="请输入租户名称" />
        </el-form-item>
        <el-form-item label="规则名称" prop="ruleKeyName">
          <el-input v-model="form.ruleKeyName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="规则分类" prop="ruleKeyCategory">
          <el-input v-model="form.ruleKeyCategory" placeholder="请输入规则分类" />
        </el-form-item>
        <el-form-item label="集群对象_scope类型" prop="scopeClusterKind">
          <el-input v-model="form.scopeClusterKind" placeholder="请输入集群对象_scope类型" />
        </el-form-item>
        <el-form-item label="集群对象_scope id" prop="scopeClusterId">
          <el-input v-model="form.scopeClusterId" placeholder="请输入集群对象_scope id" />
        </el-form-item>
        <el-form-item label="集群名称" prop="scopeClusterName">
          <el-input v-model="form.scopeClusterName" placeholder="请输入集群名称" />
        </el-form-item>
        <el-form-item label="命名空间_scope类型" prop="namespaceKind">
          <el-input v-model="form.namespaceKind" placeholder="请输入命名空间_scope类型" />
        </el-form-item>
        <el-form-item label="命名空间_scope id" prop="namespaceId">
          <el-input v-model="form.namespaceId" placeholder="请输入命名空间_scope id" />
        </el-form-item>
        <el-form-item label="命名空间_scope名称" prop="namespaceName">
          <el-input v-model="form.namespaceName" placeholder="请输入命名空间_scope名称" />
        </el-form-item>
        <el-form-item label="pod资源_scope类型" prop="podKind">
          <el-input v-model="form.podKind" placeholder="请输入pod资源_scope类型" />
        </el-form-item>
        <el-form-item label="pod资源_scope id" prop="podId">
          <el-input v-model="form.podId" placeholder="请输入pod资源_scope id" />
        </el-form-item>
        <el-form-item label="pod资源_scope名称" prop="podName">
          <el-input v-model="form.podName" placeholder="请输入pod资源_scope名称" />
        </el-form-item>
        <el-form-item label="k8s资源_scope类型" prop="resourceKind">
          <el-input v-model="form.resourceKind" placeholder="请输入k8s资源_scope类型" />
        </el-form-item>
        <el-form-item label="k8s资源_scope id" prop="resourceId">
          <el-input v-model="form.resourceId" placeholder="请输入k8s资源_scope id" />
        </el-form-item>
        <el-form-item label="k8s资源_scope名称" prop="resourceName">
          <el-input v-model="form.resourceName" placeholder="请输入k8s资源_scope名称" />
        </el-form-item>
        <el-form-item label="严重级别" prop="severity">
          <el-input v-model="form.severity" placeholder="请输入严重级别" />
        </el-form-item>
        <el-form-item label="告警tag，多个tag 逗号隔开：“，”" prop="tags">
          <el-input v-model="form.tags" placeholder="请输入告警tag，多个tag 逗号隔开：“，”" />
        </el-form-item>
        <el-form-item label="客户端IP" prop="contextClientIp">
          <el-input v-model="form.contextClientIp" placeholder="请输入客户端IP" />
        </el-form-item>
        <el-form-item label="告警上下文_客户端接口" prop="contextClientPort">
          <el-input v-model="form.contextClientPort" placeholder="请输入告警上下文_客户端接口" />
        </el-form-item>
        <el-form-item label="告警上下文_网络连接信息" prop="contextConnection">
          <el-input v-model="form.contextConnection" placeholder="请输入告警上下文_网络连接信息" />
        </el-form-item>
        <el-form-item label="告警上下文_POD ID" prop="contextPodId">
          <el-input v-model="form.contextPodId" placeholder="请输入告警上下文_POD ID" />
        </el-form-item>
        <el-form-item label="告警上下文_进程命令行" prop="contextProcCmdline">
          <el-input v-model="form.contextProcCmdline" placeholder="请输入告警上下文_进程命令行" />
        </el-form-item>
        <el-form-item label="告警上下文_进程名" prop="contextProcName">
          <el-input v-model="form.contextProcName" placeholder="请输入告警上下文_进程名" />
        </el-form-item>
        <el-form-item label="告警上下文_进程号" prop="contextProcPid">
          <el-input v-model="form.contextProcPid" placeholder="请输入告警上下文_进程号" />
        </el-form-item>
        <el-form-item label="告警上下文_父进程名" prop="contextProcPname">
          <el-input v-model="form.contextProcPname" placeholder="请输入告警上下文_父进程名" />
        </el-form-item>
        <el-form-item label="告警上下文_父进程号" prop="contextProcPpid">
          <el-input v-model="form.contextProcPpid" placeholder="请输入告警上下文_父进程号" />
        </el-form-item>
        <el-form-item label="服务IP" prop="contextServerIp">
          <el-input v-model="form.contextServerIp" placeholder="请输入服务IP" />
        </el-form-item>
        <el-form-item label="告警上下文_服务端口" prop="contextServerPort">
          <el-input v-model="form.contextServerPort" placeholder="请输入告警上下文_服务端口" />
        </el-form-item>
        <el-form-item label="系统调用名称" prop="contextSyscallName">
          <el-input v-model="form.contextSyscallName" placeholder="请输入系统调用名称" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createdAt">
          <el-date-picker
            v-model="form.createdAt"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间"
          />
        </el-form-item>
        <el-form-item label="是否命中误报 0:否  1：是" prop="isWhitelistFilter">
          <el-input
            v-model="form.isWhitelistFilter"
            placeholder="请输入是否命中白名单 0:否  1：是"
          />
        </el-form-item>
        <el-form-item label="命中白名单ids,  多个id',' 逗号隔开" prop="whitelistIds">
          <el-input v-model="form.whitelistIds" placeholder="命中白名单ids,  多个id',' 逗号隔开" />
        </el-form-item>
        <el-form-item label="处置人id" prop="disposeId">
          <el-input v-model="form.disposeId" placeholder="请输入处置人id" />
        </el-form-item>
        <el-form-item label="处置人名称" prop="disposeName">
          <el-input v-model="form.disposeName" placeholder="请输入处置人名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <mibble-reassignment
      ref="mibbleReassignmentRef"
      :mibble-reassignment-data="mibbleReassignmentData"
      @mibbleReassignmentConfirm="getList"
    />

    <el-dialog title="关联对象" :visible.sync="resourcesData.visible" width="500px" append-to-body>
      <el-table class="sa-table" :data="resourcesData.form.resources">
        <el-table-column label="集群" align="center">
          <template slot-scope="scope">
            <sa-tooltip v-if="scope.row.cluster" :content="scope.row.cluster.name" />
          </template>
        </el-table-column>
        <el-table-column label="容器" align="center">
          <template slot-scope="scope">
            <sa-tooltip v-if="scope.row.container" :content="scope.row.container.name" />
          </template>
        </el-table-column>
        <el-table-column label="节点" align="center">
          <template slot-scope="scope">
            <sa-tooltip v-if="scope.row.hostname" :content="scope.row.hostname.name" />
          </template>
        </el-table-column>
        <el-table-column label="命名空间" align="center">
          <template slot-scope="scope">
            <sa-tooltip v-if="scope.row.namespace" :content="scope.row.namespace.name" />
          </template>
        </el-table-column>
        <el-table-column label="Pod" align="center">
          <template slot-scope="scope">
            <sa-tooltip v-if="scope.row.pod" :content="scope.row.pod.name" />
          </template>
        </el-table-column>
        <el-table-column label="资源" align="center">
          <template slot-scope="scope">
            <sa-tooltip v-if="scope.row.resource" :content="scope.row.resource.name" />
          </template>
        </el-table-column>
        <el-table-column label="对象类型" align="center">
          <template slot-scope="scope">
            <sa-tooltip v-if="scope.row.scene" :content="scope.row.scene.name" />
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <MarkDialog ref="markDialog" @ok="onOkOper" />

    <WhitelistDialog ref="whitelistDialogRef" @submit="getList" />

    <InfoDialog ref="infoDialogRef" />
  </div>
</template>

<script>
import { listTz, getTz, delTz, addTz, updateTz } from '@/api/risk/resource/tz'
import RepeatNum from './RepeatNum.vue'
import AnalyseDetail from '../../components/analyse.vue'
import OperDockfile from './OperDockfile.vue'
import MarkDialog from './MarkDialog.vue'
import request from '@/utils/request'
import IpInput from '@/views/components/ipInput/ipInput.vue'
import MibbleReassignment from '@/views/system/user/MibbleReassignment.vue'
import { mapGetters } from 'vuex'
import WhitelistDialog from '@/views/risk/resource/components/WhitelistDialog/index.vue'
import InfoDialog from './components/InfoDialog/index.vue'
import AllocationActions from '@/views/risk/resource/components/AllocationActions/index.vue'
import { riskDisposeOneClickAssign } from '@/api/risk/basic/index.js'

const formLabel = [
  {
    label: '规则名称',
    field: 'ruleKeyName'
  },
  {
    label: '规则分类',
    field: 'ruleKeyCategory'
  },
  {
    label: '集群名称',
    field: 'scopeClusterName'
  },
  {
    label: '严重级别',
    field: 'severity',
    type: 'risk_level'
  },
  {
    label: '客户端IP',
    field: 'contextClientIp'
  },
  {
    label: '服务IP',
    field: 'contextServerIp'
  },
  {
    label: '系统调用名称',
    field: 'contextSyscallName'
  },
  {
    label: '处置状态',
    field: 'status',
    type: 'risk_status'
  },
  {
    label: '创建时间',
    field: 'createTime'
  }
]

export default {
  name: 'Tz',
  dicts: ['risk_status', 'risk_level', 'container_safety_one_dispose_status', 'allocation_status'],
  components: {
    IpInput,
    RepeatNum,
    AnalyseDetail,
    OperDockfile,
    MibbleReassignment,
    MarkDialog,
    WhitelistDialog,
    InfoDialog,
    AllocationActions
  },
  props: {
    statisticsData: {
      type: Object,
      default: void 0
    },
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editId: '',
      whiteListDialogShow: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      repeatIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 容器安全与微隔离-探真表格数据
      tzList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warnId: null,
        type: null,
        description: null,
        severity: null,
        status: null,
        deptName: void 0,
        systemName: void 0,
        dispatchStatus: void 0,
        riskOperatorGroupId: void 0
      },
      daterange: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }],
        updateTime: [{ required: true, message: '更新时间不能为空', trigger: 'blur' }]
      },

      repeatNumData: {
        repeatIds: null
      },

      analyse: {
        id: null,
        url: '/risk/tz/',
        type: 'tz',
        formLabel: [
          {
            label: '告警类型',
            field: 'type'
          },
          {
            label: '告警描述',
            field: 'description'
          },
          {
            label: '严重级别',
            field: 'severity',
            type: 'risk_level'
          },
          {
            label: '容器名称',
            type: '1'
          },
          {
            label: '系统调用',
            type: '2'
          },
          {
            label: '进程命令行',
            type: '3'
          },
          {
            label: '进程名',
            type: '4'
          },
          {
            label: '处置状态',
            field: 'status',
            type: 'risk_status'
          },
          {
            label: '创建时间',
            field: 'createTime'
          }
        ]
      },
      oper: {
        data: {}
      },
      detail: {
        data: {},
        formLabel: [
          // {
          //   label: '租户ID',
          //   field: 'tenantId',
          // },
          {
            label: '租户名称',
            field: 'tenantName'
          },
          {
            label: '处置状态',
            field: 'status',
            type: 'risk_status'
          },
          // {
          //   label: '重复告警IDS',
          //   field: 'repeatIds',
          // },
          {
            label: '重复次数',
            field: 'repeatNum'
          },
          {
            label: '告警ID',
            field: 'warnId'
          },
          {
            label: '规则名称',
            field: 'ruleKeyName'
          },
          {
            label: '规则分类',
            field: 'ruleKeyCategory'
          },
          {
            label: '集群对象类型',
            field: 'scopeClusterKind'
          },
          {
            label: '集群对象ID',
            field: 'scopeClusterId'
          },
          {
            label: '集群对象名称',
            field: 'scopeClusterName'
          },
          {
            label: '命名空间类型',
            field: 'namespaceKind'
          },
          {
            label: '命名空间ID',
            field: 'namespaceId'
          },
          {
            label: '命名空间名称',
            field: 'namespaceName'
          },
          {
            label: 'POD资源类型',
            field: 'podKind'
          },
          {
            label: 'POD资源ID',
            field: 'podId'
          },
          {
            label: 'POD资源名称',
            field: 'podName'
          },
          {
            label: 'k8s资源类型',
            field: 'resourceKind'
          },
          {
            label: 'k8s资源ID',
            field: 'resourceId'
          },
          {
            label: 'k8s资源名称',
            field: 'resourceName'
          },
          {
            label: '严重级别',
            field: 'severity',
            type: 'risk_level'
          },
          {
            label: '告警tag',
            field: 'tags'
          },
          {
            label: '客户端IP',
            field: 'contextClientIp'
          },
          {
            label: '客户端接口',
            field: 'contextClientPort'
          },
          {
            label: '网络连接信息',
            field: 'contextConnection'
          },
          {
            label: '告警上下文_POD ID',
            field: 'contextPodId'
          },
          {
            label: '进程命令行',
            field: 'contextProcCmdline'
          },
          {
            label: '进程名',
            field: 'contextProcName'
          },
          {
            label: '进程号',
            field: 'contextProcPid'
          },
          {
            label: '父进程名',
            field: 'contextProcPname'
          },
          {
            label: '父进程号',
            field: 'contextProcPpid'
          },
          {
            label: '服务IP',
            field: 'contextServerIp'
          },
          {
            label: '服务端口',
            field: 'contextServerPort'
          },
          {
            label: '系统调用名称',
            field: 'contextSyscallName'
          },
          {
            label: '告警触发时间',
            field: 'createdAt',
            type: 'time'
          },
          {
            label: '是否命中白名单',
            field: 'isWhitelistFilter'
          },
          {
            label: '命中白名单IDS',
            field: 'whitelistIds'
          },
          {
            label: '命中白名单IDS',
            field: 'whitelistIds'
          },
          // {
          //   label: '处置人ID',
          //   field: 'disposeId',
          // },
          {
            label: '处置人名称',
            field: 'disposeName'
          },
          {
            label: '备注',
            field: 'remarks'
          }
          // {
          //   label: '创建人',
          //   field: 'createBy',
          // },
          // {
          //   label: '创建时间',
          //   field: 'createTime',
          // },
          // {
          //   label: '更新人',
          //   field: 'updateBy',
          // },
          // {
          //   label: '更新时间',
          //   field: 'updateTime',
          // },
        ],
        isShowOper: true
      },

      mibbleReassignmentData: {
        riskType: '3',
        riskIds: [],
        userIds: []
      },

      resourcesData: {
        visible: false,
        form: {}
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    statisticsData(val) {
      this.queryParams = { ...this.queryParams, ...val }
      this.getList()
    }
  },
  created() {
    this.queryParams = { ...this.queryParams, ...this.$props.statisticsData }
    this.getList()
  },
  methods: {
    // 添加一键分配方法
    async onOneClickAssignClick(ids) {
      if (ids.length === 0) {
        this.$message.warning('请选择需要一键分配的数据')
        return
      }

      try {
        await this.$confirm('确定要执行一键分配吗?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return
      }

      // 获取组件的查询条件参数
      const queryObj = {
        ...this.queryParams
      }

      const res = await riskDisposeOneClickAssign({
        id: ids.join(','),
        riskType: '18',
        queryObj
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.getList()
      }
    },
    /** 查询容器安全与微隔离-探真列表 */
    getList() {
      this.loading = true
      listTz(this.queryParams).then((response) => {
        this.tzList = response.rows
        this.total = response.total
        this.loading = false
      })
      this.$emit('query-change', this.queryParams)
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tenantId: null,
        tenantName: null,
        status: null,
        ruleKeyName: null,
        ruleKeyCategory: null,
        scopeClusterKind: null,
        scopeClusterId: null,
        scopeClusterName: null,
        namespaceKind: null,
        namespaceId: null,
        namespaceName: null,
        podKind: null,
        podId: null,
        podName: null,
        resourceKind: null,
        resourceId: null,
        resourceName: null,
        severity: null,
        tags: null,
        contextClientIp: null,
        contextClientPort: null,
        contextConnection: null,
        contextPodId: null,
        contextProcCmdline: null,
        contextProcName: null,
        contextProcPid: null,
        contextProcPname: null,
        contextProcPpid: null,
        contextServerIp: null,
        contextServerPort: null,
        contextSyscallName: null,
        createdAt: null,
        isWhitelistFilter: null,
        whitelistIds: null,
        disposeId: null,
        disposeName: null,
        remarks: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.daterange = []
      this.onChangeDaterange()
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.repeatIds = selection.map((item) => item.repeatIds)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加容器安全与微隔离-探真'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getTz(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改容器安全与微隔离-探真'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTz(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addTz(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.repeatIds.join(',').split(',')
      // this.$modal
      //   .confirm('是否确认删除容器安全与微隔离-探真编号为"' + ids + '"的数据项？')
      //   .then(function () {
      //     return delTz(ids);
      //   })
      //   .then(() => {
      //     this.getList();
      //     this.$modal.msgSuccess('删除成功');
      //   })
      //   .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        '/risk/tz/group/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    handleStatsExport() {
      if (!this.daterange.length) {
        this.$message.warning('请选择创建时间')
        return false
      }

      this.download(
        '/risk/tz/group/exportByType',
        {
          ...this.queryParams
        },
        `导出${this.$route.meta.title}_统计报表_${new Date().getTime()}.xlsx`
      )
    },
    unknownFormat(row, column, cellValue) {
      return cellValue == null || cellValue == '' ? '未知' : cellValue
    },
    onOpenAnalyse(row) {
      this.analyse.id = row.id
      this.analyse.search = {
        podId: row.podId,
        podName: row.podName,
        contextServerIp: row.contextServerIp,
        type: row.type
      }
      // const contextLabel = {
      //   action: '⾏为',
      //   'aduit user id(auid)': 'auid',
      //   cause: '原因',
      //   cluster: '所属集群',
      //   command: '进程命令行',
      //   connection: '⽹络连接信息',
      //   container: '容器名称',
      //   'Container Image': '容器镜像',
      //   'Container Image Digest': '容器镜像Digest',
      //   'Container Image Tag': '容器镜像Tag',
      //   'Container Name': '容器名称',
      //   'Container Privileged': '是否是特权容器',
      //   'Container Type': '容器类型',
      //   container_id: '容器id',
      //   container_image_repository: '容器镜像',
      //   container_mounts: '容器挂载',
      //   'Env Key': '环境变量名称',
      //   'Event Argument OldPath': '事件旧参数路径',
      //   'Event Argument Path': '事件参数路径',
      //   'Event Arguments Name': '事件参数名称',
      //   'Event Category': '事件分类',
      //   'Event Time': '事件时间',
      //   'Event Type': '事件类型',
      //   evt_arg_exe: '可执⾏⽂件名',
      //   evt_arg_uid: '事件参数UID',
      //   evt_time: '事件时间',
      //   'FD Name': '⽂件描述符名称',
      //   'FD Type': '⽂件描述符类型',
      //   fd_cip: '客⼾端IP地址',
      //   fd_cport: '客⼾端端⼝号',
      //   fd_name: '⽂件描述符名称',
      //   fdnum: '⽂件描述符编号',
      //   fd_sip: '服务端IP地址',
      //   fd_sport: '服务端端⼝号',
      //   fd_type: '⽂件描述符类型',
      //   filePath: '⽂件路径',
      //   ggparent: '曾祖⽗进程名',
      //   gparent: '祖⽗进程名',
      //   'Host Path': '节点路径',
      //   image: '镜像',
      //   'Image not scanned': '镜像未扫描',
      //   'Image scan failure': '镜像扫描失败',
      //   image_repo_tags: '关联镜像',
      //   imageRepoTags: '关联镜像',
      //   'Is Privileged': '是否是特权容器',
      //   k8s_ns_name: '命名空间',
      //   k8s_pod_id: 'PodUid',
      //   k8s_pod_name: 'Pod名称',
      //   'Mount Path': '容器挂载⽬录',
      //   Namespace: '命名空间',
      //   original_info: '原始信息',
      //   parent: '⽗进程名',
      //   pid: '进程号',
      //   pod_id: 'Pod ID',
      //   podName: 'Pod名称',
      //   PodUid: 'PodUid',
      //   ppid: '⽗进程号',
      //   'Privileged boot image': '特权启动镜像',
      //   proc_cmdline: '进程命令行',
      //   proc_exeline: '进程命令行',
      //   proc_name: '进程名',
      //   proc_pgid: '进程组ID',
      //   proc_pid: '进程号',
      //   proc_pname: '⽗进程名称',
      //   proc_ppid: '⽗进程号',
      //   'Process Controlling Terminal': '进程控制台',
      //   'Process FD Count': '进程⽂件描述符数',
      //   'Process LoginShell Pid': '初始shell进程ID',
      //   procName: '进程名',
      //   procPname: '⽗进程名称',
      //   'Read Only': '是否只读',
      //   reason: '原因',
      //   'Risky Mounted HostPathes': '⻛险挂载路径',
      //   'Risky POSIX Capability': '存在⻛险的POSIX权限',
      //   risky_mounted_host_pathes: '⻛险挂载路径',
      //   'Role Created Time': 'Role创建时间',
      //   ruleCategory: '规则类型',
      //   'Seccomp Enabled': '开启了Seccomp',
      //   SeccompType: 'Seccomp类型',
      //   'SELinux Enabled': '开启了SELinux',
      //   syscall: '系统调⽤',
      //   'syscall.type': '系统调⽤',
      //   terminal: '终端名称',
      //   user: '⽤⼾名',
      //   user_loginuid: 'auid',
      //   user_name: '⽤⼾名',
      //   user_uid: '⽤⼾UID',
      //   'Volume Name': '卷名称',
      //   syscall_name: '系统调⽤名称',
      //   client_ip: '客⼾端ip',
      //   client_port: '客⼾端端⼝',
      //   server_ip: '服务端ip',
      //   server_port: '服务端端⼝',
      //   container_name: '容器名称',
      //   proc_vpgid: '进程组id',
      //   proc_tty: '进程终端',
      //   'proc_aname[0]': '当前进程名',
      //   'proc_aname[1]': '⽗进程名',
      //   'proc_aname[2]': '祖⽗进程名',
      //   'proc_aname[3]': '曾祖⽗进程名',
      //   'proc_aname[4]': '⾼祖⽗进程名',
      //   'proc_aname[5]': '⾼祖⽗的⽗进程名',
      //   'proc_aname[6]': '⾼祖⽗的祖⽗进程名',
      //   'proc_aname[7]': '⾼祖⽗的曾祖⽗进程名',
      //   container_info: '容器信息',
      //   evt_type: '系统调⽤名',
      //   container_image_tag: '容器镜像tag',
      //   evt_args: '系统调⽤所有参数',
      //   evt_arg_name: '系统调⽤name参数',
      //   evt_arg_linkpath: '系统调⽤linkpath参数',
      //   evt_arg_target: '系统调⽤target参数',
      //   evt_arg_filename: '系统调⽤filename参数',
      //   evt_arg_path: '系统调⽤path参数',
      //   evt_arg_oldpath: '系统调⽤oldpath参数',
      //   evt_arg_fd: '系统调⽤fd参数',
      //   evt_arg_mode: '系统调⽤mode参数',
      //   fd_sip_name: '服务端域名',
      //   syscall_type: '系统调⽤类型',
      //   fd_l4proto: 'socket协议名',
      //   event_category: '事件分类',
      //   checkResult: '检测结果',
      //   runCommand: '执⾏命令',
      //   commandOutput: '执⾏结果',
      // };
      // this.analyse.formLabel = [
      //   {
      //     label: '告警事件ID',
      //     field: 'warnId',
      //   },
      //   {
      //     label: '告警类型',
      //     field: 'type',
      //   },
      //   {
      //     label: '告警描述',
      //     field: 'description',
      //   },
      //   {
      //     label: '严重级别',
      //     field: 'severity',
      //     type: 'risk_level',
      //   },
      //   {
      //     label: '重复次数',
      //     field: 'repeatNum',
      //   },
      // ];
      // for (var key in contextLabel) {
      //   if (row.context[key]) {
      //     this.analyse.formLabel.push({
      //       label: contextLabel[key],
      //       field: key,
      //     });
      //   }
      // }
      this.$refs.analyse.show()
    },
    onOper(row) {
      this.$refs.operDockfile.show(row ? String(row.id).split(',') : this.ids)
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'mibbleSendBack':
          this.onMibbleSendBack(row)
          break
        case 'mibbleReassignment':
          this.onMibbleReassignment(row)
          break
        case 'analyse':
          this.onOpenAnalyse(row)
          break
        case 'resources':
          this.onOpenResources(row)
          break
        case 'markEnd':
          this.onMark(row)
          break
        case 'joinWhiteList':
          this.$refs.whitelistDialogRef.open({
            id: row.id,
            containerSafeType: 0
          })
          break
        default:
          break
      }
    },
    onDetail(row) {
      // const contextLabel = {
      //   action: '⾏为',
      //   'aduit user id(auid)': 'auid',
      //   cause: '原因',
      //   cluster: '所属集群',
      //   command: '进程命令行',
      //   connection: '⽹络连接信息',
      //   container: '容器名称',
      //   'Container Image': '容器镜像',
      //   'Container Image Digest': '容器镜像Digest',
      //   'Container Image Tag': '容器镜像Tag',
      //   'Container Name': '容器名称',
      //   'Container Privileged': '是否是特权容器',
      //   'Container Type': '容器类型',
      //   container_id: '容器id',
      //   container_image_repository: '容器镜像',
      //   container_mounts: '容器挂载',
      //   'Env Key': '环境变量名称',
      //   'Event Argument OldPath': '事件旧参数路径',
      //   'Event Argument Path': '事件参数路径',
      //   'Event Arguments Name': '事件参数名称',
      //   'Event Category': '事件分类',
      //   'Event Time': '事件时间',
      //   'Event Type': '事件类型',
      //   evt_arg_exe: '可执⾏⽂件名',
      //   evt_arg_uid: '事件参数UID',
      //   evt_time: '事件时间',
      //   'FD Name': '⽂件描述符名称',
      //   'FD Type': '⽂件描述符类型',
      //   fd_cip: '客⼾端IP地址',
      //   fd_cport: '客⼾端端⼝号',
      //   fd_name: '⽂件描述符名称',
      //   fdnum: '⽂件描述符编号',
      //   fd_sip: '服务端IP地址',
      //   fd_sport: '服务端端⼝号',
      //   fd_type: '⽂件描述符类型',
      //   filePath: '⽂件路径',
      //   ggparent: '曾祖⽗进程名',
      //   gparent: '祖⽗进程名',
      //   'Host Path': '节点路径',
      //   image: '镜像',
      //   'Image not scanned': '镜像未扫描',
      //   'Image scan failure': '镜像扫描失败',
      //   image_repo_tags: '关联镜像',
      //   imageRepoTags: '关联镜像',
      //   'Is Privileged': '是否是特权容器',
      //   k8s_ns_name: '命名空间',
      //   k8s_pod_id: 'PodUid',
      //   k8s_pod_name: 'Pod名称',
      //   'Mount Path': '容器挂载⽬录',
      //   Namespace: '命名空间',
      //   original_info: '原始信息',
      //   parent: '⽗进程名',
      //   pid: '进程号',
      //   pod_id: 'Pod ID',
      //   podName: 'Pod名称',
      //   PodUid: 'PodUid',
      //   ppid: '⽗进程号',
      //   'Privileged boot image': '特权启动镜像',
      //   proc_cmdline: '进程命令行',
      //   proc_exeline: '进程命令行',
      //   proc_name: '进程名',
      //   proc_pgid: '进程组ID',
      //   proc_pid: '进程号',
      //   proc_pname: '⽗进程名称',
      //   proc_ppid: '⽗进程号',
      //   'Process Controlling Terminal': '进程控制台',
      //   'Process FD Count': '进程⽂件描述符数',
      //   'Process LoginShell Pid': '初始shell进程ID',
      //   procName: '进程名',
      //   procPname: '⽗进程名称',
      //   'Read Only': '是否只读',
      //   reason: '原因',
      //   'Risky Mounted HostPathes': '⻛险挂载路径',
      //   'Risky POSIX Capability': '存在⻛险的POSIX权限',
      //   risky_mounted_host_pathes: '⻛险挂载路径',
      //   'Role Created Time': 'Role创建时间',
      //   ruleCategory: '规则类型',
      //   'Seccomp Enabled': '开启了Seccomp',
      //   SeccompType: 'Seccomp类型',
      //   'SELinux Enabled': '开启了SELinux',
      //   syscall: '系统调⽤',
      //   'syscall.type': '系统调⽤',
      //   terminal: '终端名称',
      //   user: '⽤⼾名',
      //   user_loginuid: 'auid',
      //   user_name: '⽤⼾名',
      //   user_uid: '⽤⼾UID',
      //   'Volume Name': '卷名称',
      //   syscall_name: '系统调⽤名称',
      //   client_ip: '客⼾端ip',
      //   client_port: '客⼾端端⼝',
      //   server_ip: '服务端ip',
      //   server_port: '服务端端⼝',
      //   container_name: '容器名称',
      //   proc_vpgid: '进程组id',
      //   proc_tty: '进程终端',
      //   'proc_aname[0]': '当前进程名',
      //   'proc_aname[1]': '⽗进程名',
      //   'proc_aname[2]': '祖⽗进程名',
      //   'proc_aname[3]': '曾祖⽗进程名',
      //   'proc_aname[4]': '⾼祖⽗进程名',
      //   'proc_aname[5]': '⾼祖⽗的⽗进程名',
      //   'proc_aname[6]': '⾼祖⽗的祖⽗进程名',
      //   'proc_aname[7]': '⾼祖⽗的曾祖⽗进程名',
      //   container_info: '容器信息',
      //   evt_type: '系统调⽤名',
      //   container_image_tag: '容器镜像tag',
      //   evt_args: '系统调⽤所有参数',
      //   evt_arg_name: '系统调⽤name参数',
      //   evt_arg_linkpath: '系统调⽤linkpath参数',
      //   evt_arg_target: '系统调⽤target参数',
      //   evt_arg_filename: '系统调⽤filename参数',
      //   evt_arg_path: '系统调⽤path参数',
      //   evt_arg_oldpath: '系统调⽤oldpath参数',
      //   evt_arg_fd: '系统调⽤fd参数',
      //   evt_arg_mode: '系统调⽤mode参数',
      //   fd_sip_name: '服务端域名',
      //   syscall_type: '系统调⽤类型',
      //   fd_l4proto: 'socket协议名',
      //   event_category: '事件分类',
      //   checkResult: '检测结果',
      //   runCommand: '执⾏命令',
      //   commandOutput: '执⾏结果'
      // }
      // this.detail.formLabel = [
      //   {
      //     label: '告警事件ID',
      //     field: 'warnId'
      //   },
      //   {
      //     label: '告警类型',
      //     field: 'type'
      //   },
      //   {
      //     label: '告警描述',
      //     field: 'description'
      //   },
      //   {
      //     label: '严重级别',
      //     field: 'severity',
      //     type: 'risk_level'
      //   },
      //   {
      //     label: '重复次数',
      //     field: 'repeatNum'
      //   }
      // ]
      // for (var key in contextLabel) {
      //   if (row?.context?.[key]) {
      //     this.detail.formLabel.push({
      //       label: contextLabel[key],
      //       field: key
      //     })
      //   }
      // }

      // this.detail.data = { ...row, ...(row.context || {}) }
      // this.$refs.detail.show()

      this.$refs.infoDialogRef.open(row)
    },
    checkSelectable(row) {
      return row.status != 2
    },
    onOkOper() {
      this.getList()
      this.$emit('updateMiddleHandleStatistics')
    },
    onRepeatNum(row) {
      this.repeatNumData.repeatIds = row.repeatIds
      this.$refs.repeatNumRef.show()
    },
    onChangeDaterange() {
      if (this.daterange && this.daterange.length > 0) {
        this.queryParams.startTime = this.daterange[0]
        this.queryParams.endTime = this.daterange[1]
      } else {
        delete this.queryParams.startTime
        delete this.queryParams.endTime
      }
    },
    onMibbleReassignment(row) {
      this.mibbleReassignmentData.riskIds = row.id ? [row.id] : this.ids
      this.$refs.mibbleReassignmentRef.show()
    },
    onMibbleSendBack(row) {
      request({
        url: '/risk/warn/mibbleSendBack',
        method: 'post',
        data: {
          riskType: '3',
          riskIds: row.id ? [row.id] : this.ids,
          userId: this.userInfo.userId
        }
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        if (response.code === 200) {
          this.getList()
        }
      })
    },
    onOpenResources(row) {
      this.resourcesData.form = row
      this.resourcesData.visible = true
    },
    onMark(row) {
      if (row) {
        this.$refs.markDialog.show(String(row.id).split(','), row.status)
      } else {
        this.$refs.markDialog.show(this.ids)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .prompt {
    position: relative;
    .prompt-box {
      position: absolute;
      left: -28px;
      top: 40px;
    }
    .prompt-box-content {
      position: relative;
      width: 200px;
      height: auto;
      z-index: 100;
      border: solid 1px #dfe4ed;
      border-radius: 4px;
      background-color: #ffffff;
      padding: 8px;
      white-space: nowrap;
      overflow-x: scroll;
    }
    .ovflow-y {
      height: 260px;
      overflow-y: scroll;
    }
    .prompt-box-sanjiao {
      position: absolute;
      top: -9px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 101;
    }
    .sanjiao {
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 10px 10px;
      border-color: transparent transparent #dfe4ed;
      position: relative;
    }
    .sanjiao::after {
      content: '';
      border-style: solid;
      border-width: 0 9px 9px;
      border-color: transparent transparent #fff;
      position: absolute;
      top: 1px;
      left: -9px;
    }
    .input-msg-item {
      padding-bottom: 6px;
      // border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
    }
  }
</style>
