<template>
  <div class="page-main" :class="autoHeight ? 'h-full overflow-hidden' : ''">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      class="sa-query"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="报警类型名称" prop="name" class="">
        <div class="prompt">
          <el-input
            slot="reference"
            v-model="queryParams.name"
            placeholder="请输入"
            clearable
            @blur="onBlurName"
            @input="onChangeNameInput"
            @keyup.enter.native="handleQuery"
          />
          <div v-if="nameInputData.length > 0 && isName" class="prompt-box">
            <div class="prompt-box-sanjiao">
              <div class="sanjiao" />
            </div>
            <div class="prompt-box-content" :class="{ 'ovflow-y': nameInputData.length > 6 }">
              <div
                v-for="(item, index) in nameInputData"
                :key="index"
                class="input-msg-item"
                @click="onNameInput(item)"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="集群名称" prop="cluster">
        <div class="prompt">
          <el-input
            slot="reference"
            v-model="queryParams.cluster"
            placeholder="请输入"
            clearable
            @blur="onBlurScope"
            @input="onChangeScope"
            @keyup.enter.native="handleQuery"
          />
          <div v-if="scopeData.length > 0 && isScope" class="prompt-box">
            <div class="prompt-box-sanjiao">
              <div class="sanjiao" />
            </div>
            <div class="prompt-box-content" :class="{ 'ovflow-y': scopeData.length > 6 }">
              <div
                v-for="(item, index) in scopeData"
                :key="index"
                class="input-msg-item"
                @click="onScopeMsg(item)"
              >
                {{ item }}
              </div>
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="部门名称" prop="deptName">
        <CommonDepartmentSelect
          v-model="queryParams.deptName"
          placeholder="请输入"
          clearable
          return-name
          @change="()=> queryParams.systemName = void 0"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="业务系统" prop="systemName">
        <CommonSystemSelect
          v-model="queryParams.systemName"
          placeholder="请输入"
          return-name
          clearable
          :params="{
            deptName: queryParams.deptName
          }"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="风险等级" prop="level">
        <el-select
          v-model="queryParams.level"
          placeholder="请选择风险等级"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.risk_level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="数据推送来源" prop="syslogTag" class="">
        <DictSelect v-model="queryParams.syslogTag" dict-type="syslogTag" remote />
      </el-form-item>

      <el-form-item label="处置状态" prop="status">
        <EleSelectDict
          v-model="queryParams.status"
          clearable
          dict-type="container_safety_one_dispose_status"
          remote
          placeholder="请选择处置状态"
          @change="handleQuery"
        ></EleSelectDict>
      </el-form-item>

      <el-form-item label="分配状态" prop="dispatchStatus">
        <EleSelectDict
          v-model="queryParams.dispatchStatus"
          :options="dict.type.allocation_status"
          placeholder="请选择分配状态"
          @change="handleQuery"
        ></EleSelectDict>
      </el-form-item>

      <el-form-item label="处置分组" prop="riskOperatorGroupId">
        <DisposeGroupSelect
          ref="disposeGroupSelectRef"
          v-model="queryParams.riskOperatorGroupId"
          placeholder="选择处置分组"
          clearable
          :params="{
            groupKey: 'basic_type_middle_dim',
            disposeType: '容器安全',
          }"
        />
      </el-form-item>

      <el-form-item class="create-time datetimerange" label="创建时间" prop="daterange">
        <el-date-picker
          v-model="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          class="!w-full"
          @change="onChangeDaterange"
        />
      </el-form-item>
      <el-form-item label="节点IP" prop="hostIp" class="ip">
        <el-input
          v-model="queryParams.hostIp"
          placeholder="节点IP(多IP请用&quot;,&quot;分开)"
          clearable
          @input="inputHandler"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="节点IP区间" class="ip-item">
        <ip-input ref="ipRef" @startIp="getStartIp" @endIp="getEndIp" />
      </el-form-item>
      <el-form-item prop="$action" class="query-handle">
        <el-button type="primary" plain icon="el-icon-search" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh-left" @click="resetQuery">重置</el-button>
        <EleSheetSearchMore :parent-ref="()=> $refs.queryForm" />
      </el-form-item>
    </el-form>

    <div class="sa-title">
      <sa-toolbar :show-search.sync="showSearch" @queryTable="getList" />
      <el-button
        v-hasPermi="['risk:xy:group:export']"
        type="primary"
        icon="el-icon-download"
        @click="handleExport"
      >导出</el-button>
      <el-button
        v-hasPermi="['risk:xy:group:exportStats']"
        type="primary"
        icon="el-icon-download"
        @click="handleStatsExport"
      >导出统计报表</el-button>
      <!-- <el-button
        v-if="$checkPermi(['risk:warn:xy:dispose'])"
        type="primary"
        :disabled="multiple"
        @click="onOper()"
        ><IconHandyman class="" />处置</el-button
      > -->
      <el-button
        v-hasPermi="['risk:warn:oneClickAssign']"
        type="success"
        :disabled="multiple"
        @click="onOneClickAssignClick(ids)"
      ><i class="el-icon-thumb" />一键分配</el-button>

      <el-button
        v-if="$checkPermi(['risk:turnoverRecord:xy:goBackRiskData'])"
        type="warning"
        :disabled="multiple"
        @click="onMibbleSendBack"
      ><IconUndo class="el-icon" />退回</el-button>
      <el-button
        v-if="$checkPermi(['risk:turnoverRecord:xy:transferRiskData'])"
        type="warning"
        :disabled="multiple"
        @click="onMibbleReassignment"
      ><IconForward class="" />转派</el-button>

      <template v-if="$checkPermi(['risk:xy:group:markers'])">
        <ele-tooltip-button type="success" :disabled="multiple" content="列表所选中的进行标记" @click="onBatchMark('select')"><IconCheck class="icon" />标记</ele-tooltip-button>
        <ele-tooltip-button type="success" content="当前条件查询的进行标记" @click="onBatchMark('all')"><IconDoneAll class="" /> 全量标记</ele-tooltip-button>
      </template>

      <AllocationActions
        v-if="$checkPermi(['risk:middle:xy:assign'])"
        v-bind="{
          multiple,
          ids,
          riskType: '6',
          disposeType: '容器安全',
          getList
        }"
      />

      <!-- <el-button
        type="danger"
        plain
        :disabled="multiple"
        @click="handleDelete"
        v-hasPermi="['resource:xy:remove']"
        ><i class="iconfont icon-piliangshanchu"></i>批量删除</el-button
      > -->
    </div>

    <div v-full:height="autoHeight" class="">
      <el-table
        ref="tableRef"
        v-loading="loading"
        v-bind="{
          ...(autoHeight ? { height: '100%' } : {})
        }"
        class="sa-table"
        :data="xyList"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" reserve-selection />
        <el-table-column
          label="报警类型名称"
          align="center"
          prop="name"
          min-width="230"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="集群名称"
          align="center"
          prop="cluster"
          min-width="120"
          :show-overflow-tooltip="true"
        />

        <el-table-column
          label="部门名称"
          align="center"
          prop="deptName"
          min-width="120"
          :formatter="unknownFormat"
        />
        <el-table-column
          label="业务系统"
          align="center"
          prop="systemName"
          min-width="120"
          :formatter="unknownFormat"
        />

        <el-table-column label="风险等级" align="center" min-width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.risk_level" :value="scope.row.level" />
          </template>
        </el-table-column>

        <!-- <el-table-column
        label="服务名称"
        align="center"
        prop="serviceName"
        min-width="120"
        :show-overflow-tooltip="true"
        :formatter="unknownFormat"
      /> -->
        <el-table-column
          label="镜像名称"
          align="center"
          prop="imageName"
          min-width="140"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="容器名称"
          align="center"
          prop="containerName"
          min-width="120"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="节点IP"
          align="center"
          prop="hostIp"
          min-width="120"
          :formatter="unknownFormat"
        />
        <el-table-column label="重复次数" align="center" width="140" min-width="120">
          <template slot-scope="scope">
            <!-- v-hasPermi="['risk:xy:batch']" -->
            <el-button
              v-if="scope.row.repeatNum"
              size="mini"
              type="text"
              @click="onRepeatNum(scope.row)"
            >
              {{ scope.row.repeatNum }}
            </el-button>
            <span v-else class=""> - </span>
          </template>
        </el-table-column>

        <el-table-column label="数据推送来源" align="center" min-width="120">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.syslogTag" :value="scope.row.syslogTag" />
          </template>
        </el-table-column>

        <el-table-column label="处置状态" align="center" min-width="120">
          <template slot-scope="scope">
            <EleTagDict :value="scope.row.status" :options="dict.type.container_safety_one_dispose_status" />
          </template>
        </el-table-column>

        <el-table-column label="分配状态" align="center" min-width="120">
          <template slot-scope="scope">
            <EleTagDict :value="scope.row.dispatchStatus" :options="dict.type.allocation_status" />
          </template>
        </el-table-column>

        <el-table-column label="处置分组" prop="riskOperatorGroupName" align="center" min-width="120">
        </el-table-column>

        <el-table-column label="创建时间" align="center" prop="createTime" min-width="180" />
        <el-table-column fixed="right" label="操作" align="center" min-width="140">
          <template slot-scope="scope">
            <el-button v-hasPermi="['risk:warn:oneClickAssign']" type="text" size="mini" @click="onOneClickAssignClick([scope.row.id])">一键分配</el-button>

            <el-button
              v-if="$checkPermi(['risk:warn:xy:dispose']) && scope.row.status != 2"
              size="mini"
              type="text"
              @click="onOper(scope.row)"
            >处置</el-button>
            <el-button v-if="$checkPermi(['risk:xy:group:query'])" size="mini" type="text" @click="onDetail(scope.row)">查看</el-button>
            <el-dropdown @command="(command) => handleCommand(command, scope.row)">
              <el-button size="mini" type="text">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="$checkPermi(['risk:turnoverRecord:xy:goBackRiskData'])" command="mibbleSendBack">
                  <el-button v-if="scope.row.status != 2" size="mini" type="text">退回</el-button>
                </el-dropdown-item>
                <el-dropdown-item v-if="$checkPermi(['risk:turnoverRecord:xy:transferRiskData'])" command="mibbleReassignment">
                  <el-button v-if="scope.row.status != 2" size="mini" type="text">转派</el-button>
                </el-dropdown-item>
                <el-dropdown-item command="analyse">
                  <el-button
                    v-hasPermi="['risk:xy:relevance:list']"
                    size="mini"
                    type="text"
                  >分析</el-button>
                </el-dropdown-item>
                <el-dropdown-item v-if="$checkPermi(['risk:xy:group:markers'])" command="markEnd">
                  <el-button size="mini" type="text">标记</el-button>
                </el-dropdown-item>
                <el-dropdown-item v-if="$checkPermi(['risk:xy:group:whiteList'])" command="joinWhiteList">
                  <el-button size="mini" type="text">加入白名单</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 重复次数 -->
    <repeat-num ref="repeatNumRef" :repeat-num-data="repeatNumData" />

    <!-- 分析 -->
    <analyse-detail ref="analyse" :analyse-data="analyse" />

    <!-- 处置 -->
    <OperDockfile ref="operDockfile" @ok="onOkOper" />

    <!-- 详情 -->
    <sa-detail ref="detail" :detail-data="detail" />

    <!-- 添加或修改容器安全与微隔离-小佑对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="租户id" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户id" />
        </el-form-item>
        <el-form-item label="租户名称" prop="tenantName">
          <el-input v-model="form.tenantName" placeholder="请输入租户名称" />
        </el-form-item>
        <el-form-item label="集群名称" prop="clusterName">
          <el-input v-model="form.clusterName" placeholder="请输入集群名称" />
        </el-form-item>
        <el-form-item label="报警类型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入报警类型名称" />
        </el-form-item>
        <el-form-item label="报警类型描述" prop="description">
          <el-input v-model="form.description" placeholder="请输入报警类型描述" />
        </el-form-item>
        <el-form-item label="报警信息id" prop="warnInfoId">
          <el-input v-model="form.warnInfoId" placeholder="请输入报警信息id" />
        </el-form-item>
        <el-form-item label="风险等级" prop="level">
          <el-input v-model="form.level" placeholder="请输入风险等级" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createdAt">
          <el-date-picker
            v-model="form.createdAt"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择创建时间"
          />
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedAt">
          <el-date-picker
            v-model="form.updatedAt"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更新时间"
          />
        </el-form-item>
        <el-form-item label="处理时间" prop="deletedAt">
          <el-date-picker
            v-model="form.deletedAt"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择处理时间"
          />
        </el-form-item>
        <el-form-item label="上报报警信息的主机名称" prop="hostName">
          <el-input v-model="form.hostName" placeholder="请输入上报报警信息的主机名称" />
        </el-form-item>
        <el-form-item label="上报报警信息的主机ID" prop="hostId">
          <el-input v-model="form.hostId" placeholder="请输入上报报警信息的主机ID" />
        </el-form-item>
        <el-form-item label="容器id" prop="containerId">
          <el-input v-model="form.containerId" placeholder="请输入容器id" />
        </el-form-item>
        <el-form-item label="容器名称" prop="containerName">
          <el-input v-model="form.containerName" placeholder="请输入容器名称" />
        </el-form-item>
        <el-form-item label="镜像名称" prop="imageName">
          <el-input v-model="form.imageName" placeholder="请输入镜像名称" />
        </el-form-item>
        <el-form-item
          label="镜像仓库名，当镜像仓库名为”N/A”时，表示该镜像是非仓库镜像"
          prop="registryName"
        >
          <el-input
            v-model="form.registryName"
            placeholder="请输入镜像仓库名，当镜像仓库名为”N/A”时，表示该镜像是非仓库镜像"
          />
        </el-form-item>
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="进程名" prop="procName">
          <el-input v-model="form.procName" placeholder="请输入进程名" />
        </el-form-item>
        <el-form-item label="进程命令行" prop="procCmd">
          <el-input v-model="form.procCmd" placeholder="请输入进程命令行" />
        </el-form-item>
        <el-form-item label="进程命令行" prop="pprocCmd">
          <el-input v-model="form.pprocCmd" placeholder="请输入进程命令行" />
        </el-form-item>
        <el-form-item label="文件路径" prop="filePath">
          <el-input v-model="form.filePath" placeholder="请输入文件路径" />
        </el-form-item>
        <el-form-item label="目的ip" prop="dstIp">
          <el-input v-model="form.dstIp" placeholder="请输入目的ip" />
        </el-form-item>
        <el-form-item label="目的端口" prop="dstPort">
          <el-input v-model="form.dstPort" placeholder="请输入目的端口" />
        </el-form-item>
        <el-form-item label="源端口" prop="srcPort">
          <el-input v-model="form.srcPort" placeholder="请输入源端口" />
        </el-form-item>
        <el-form-item label="报警邮件发送次数" prop="sendNum">
          <el-input v-model="form.sendNum" placeholder="请输入报警邮件发送次数" />
        </el-form-item>
        <el-form-item label="源ip" prop="srcIp">
          <el-input v-model="form.srcIp" placeholder="请输入源ip" />
        </el-form-item>
        <el-form-item label="集群id" prop="clusterId">
          <el-input v-model="form.clusterId" placeholder="请输入集群id" />
        </el-form-item>
        <el-form-item label="集群名称" prop="clusterName">
          <el-input v-model="form.clusterName" placeholder="请输入集群名称" />
        </el-form-item>
        <el-form-item label="触发镜像规则，规则所属 ids" prop="imageSecRuleId">
          <el-input v-model="form.imageSecRuleId" placeholder="请输入触发镜像规则，规则所属 ids" />
        </el-form-item>
        <el-form-item label="重复次数" prop="repeatNum">
          <el-input v-model="form.repeatNum" placeholder="请输入重复次数" />
        </el-form-item>
        <el-form-item label="唯一值" prop="unionStr">
          <el-input v-model="form.unionStr" placeholder="请输入唯一值" />
        </el-form-item>
        <el-form-item label="触发容器规则，规则所属ids" prop="containerSecRuleId">
          <el-input
            v-model="form.containerSecRuleId"
            placeholder="请输入触发容器规则，规则所属ids"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="命名空间名称" prop="namespaceName">
          <el-input v-model="form.namespaceName" placeholder="请输入命名空间名称" />
        </el-form-item>
        <el-form-item
          label="在文件类型报警的时候用来记录产生创建文件时间时间或者说产生这个文件报警的时间"
          prop="fileCreateTime"
        >
          <el-date-picker
            v-model="form.fileCreateTime"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择在文件类型报警的时候用来记录产生创建文件时间时间或者说产生这个文件报警的时间"
          />
        </el-form-item>
        <el-form-item label="修改文件时间" prop="fileModifyTime">
          <el-date-picker
            v-model="form.fileModifyTime"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择修改文件时间"
          />
        </el-form-item>
        <el-form-item label="文件md5值" prop="fileMd5">
          <el-input v-model="form.fileMd5" placeholder="请输入文件md5值" />
        </el-form-item>
        <el-form-item label="文件SHA256值" prop="fileSha256">
          <el-input v-model="form.fileSha256" placeholder="请输入文件SHA256值" />
        </el-form-item>
        <el-form-item label="进程id，所有和进程有关的报警都需要用" prop="pid">
          <el-input v-model="form.pid" placeholder="请输入进程id，所有和进程有关的报警都需要用" />
        </el-form-item>
        <el-form-item label="父进程名称" prop="pprocName">
          <el-input v-model="form.pprocName" placeholder="请输入父进程名称" />
        </el-form-item>
        <el-form-item label="节点ip" prop="hostIp">
          <el-input v-model="form.hostIp" placeholder="请输入节点ip" />
        </el-form-item>
        <el-form-item label="下载路径" prop="downloadPath">
          <el-input v-model="form.downloadPath" placeholder="请输入下载路径" />
        </el-form-item>
        <el-form-item label="src下命名空间名称" prop="srcNamespace">
          <el-input v-model="form.srcNamespace" placeholder="请输入src下命名空间名称" />
        </el-form-item>
        <el-form-item label="dst下命名空间名称" prop="dstNamespace">
          <el-input v-model="form.dstNamespace" placeholder="请输入dst下命名空间名称" />
        </el-form-item>
        <el-form-item label="${comment}" prop="srcObject">
          <el-input v-model="form.srcObject" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="dstObject">
          <el-input v-model="form.dstObject" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="protocol">
          <el-input v-model="form.protocol" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="detectEngine">
          <el-input v-model="form.detectEngine" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="资源ips,多个ip逗号隔开" prop="sourceIps">
          <el-input v-model="form.sourceIps" placeholder="请输入资源ips,多个ip逗号隔开" />
        </el-form-item>
        <el-form-item label="${comment}" prop="verb">
          <el-input v-model="form.verb" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="requestObject">
          <el-input v-model="form.requestObject" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="用户组集合，多个组用都逗号隔开" prop="userGroups">
          <el-input v-model="form.userGroups" placeholder="请输入用户组集合，多个组用都逗号隔开" />
        </el-form-item>
        <el-form-item label="用户代理商" prop="userAgent">
          <el-input v-model="form.userAgent" placeholder="请输入用户代理商" />
        </el-form-item>
        <el-form-item label="注释" prop="annotations">
          <el-input v-model="form.annotations" placeholder="请输入注释" />
        </el-form-item>
        <el-form-item label="请求的url" prop="requestUri">
          <el-input v-model="form.requestUri" placeholder="请输入请求的url" />
        </el-form-item>
        <el-form-item label="方法" prop="method">
          <el-input v-model="form.method" placeholder="请输入方法" />
        </el-form-item>
        <el-form-item label="服务名称" prop="serviceName">
          <el-input v-model="form.serviceName" placeholder="请输入服务名称" />
        </el-form-item>
        <el-form-item label="${comment}" prop="args">
          <el-input v-model="form.args" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="政策名称" prop="policyName">
          <el-input v-model="form.policyName" placeholder="请输入政策名称" />
        </el-form-item>
        <el-form-item label="app服务id" prop="appServiceId">
          <el-input v-model="form.appServiceId" placeholder="请输入app服务id" />
        </el-form-item>
        <el-form-item label="政策id" prop="policyId">
          <el-input v-model="form.policyId" placeholder="请输入政策id" />
        </el-form-item>
        <el-form-item label="是否阻塞 0：否 1：是" prop="packet">
          <el-input v-model="form.packet" placeholder="请输入是否阻塞 0：否 1：是" />
        </el-form-item>
        <el-form-item label="包" prop="isBlock">
          <el-input v-model="form.isBlock" placeholder="请输入包" />
        </el-form-item>
        <el-form-item label="服务端口" prop="servicePort">
          <el-input v-model="form.servicePort" placeholder="请输入服务端口" />
        </el-form-item>
        <el-form-item label="标签" prop="tag">
          <el-input v-model="form.tag" placeholder="请输入标签" />
        </el-form-item>
        <el-form-item label="标签-评论" prop="tagComment">
          <el-input v-model="form.tagComment" placeholder="请输入标签-评论" />
        </el-form-item>
        <el-form-item label="处置人id" prop="disposeId">
          <el-input v-model="form.disposeId" placeholder="请输入处置人id" />
        </el-form-item>
        <el-form-item label="处置人名称" prop="disposeName">
          <el-input v-model="form.disposeName" placeholder="请输入处置人名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>

    <mibble-reassignment
      ref="mibbleReassignmentRef"
      :mibble-reassignment-data="mibbleReassignmentData"
      @mibbleReassignmentConfirm="getList"
    />
    <WhitelistDialog ref="whitelistDialogRef" @submit="getList" />
    <MarkDialog ref="markDialog" @ok="onOkOper" />

    <InfoDialog ref="infoDialogRef" />
  </div>
</template>

<script>
import { listXy, getXy, delXy, addXy, updateXy } from '@/api/risk/resource/xy'
import RepeatNum from './RepeatNum.vue'
import AnalyseDetail from '../../components/analyse.vue'
import OperDockfile from './OperDockfile.vue'
import MarkDialog from './MarkDialog.vue'
import IpInput from '@/views/components/ipInput/ipInput.vue'
import request from '@/utils/request'
import MibbleReassignment from '@/views/system/user/MibbleReassignment.vue'
import { mapGetters } from 'vuex'
import WhitelistDialog from '@/views/risk/resource/components/WhitelistDialog/index.vue'
import DictSelect from '@/components/DictSelect/index.vue'
import InfoDialog from './components/InfoDialog/index.vue'
import AllocationActions from '@/views/risk/resource/components/AllocationActions/index.vue'
import { riskDisposeOneClickAssign } from '@/api/risk/basic/index.js'

const formLabel = [
  {
    label: '报警类型名称',
    field: 'name'
  },
  {
    label: '集群名称',
    field: 'cluster'
  },
  {
    label: '风险等级',
    field: 'level',
    type: 'risk_level'
  },
  {
    label: '服务名称',
    field: 'serviceName'
  },
  {
    label: '镜像名称',
    field: 'imageName'
  },
  {
    label: '容器名称',
    field: 'containerName'
  },
  {
    label: '政策名称',
    field: 'policyName'
  },
  {
    label: '重复次数',
    field: 'repeatNum'
  },
  {
    label: '处置状态',
    field: 'status',
    type: 'risk_status'
  },
  {
    label: '创建时间',
    field: 'createTime'
  }
]

export default {
  name: 'Xy',
  dicts: ['risk_status', 'risk_level', 'syslogTag', 'container_safety_one_dispose_status', 'allocation_status'],
  components: {
    IpInput,
    RepeatNum,
    AnalyseDetail,
    OperDockfile,
    MibbleReassignment,
    MarkDialog,
    WhitelistDialog,
    DictSelect,
    InfoDialog,
    AllocationActions
  },
  props: {
    statisticsData: {
      type: Object,
      default: void 0
    },
    autoHeight: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editId: '',
      whiteListDialogShow: false,
      isScope: true,
      isName: true,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      repeatIds: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 容器安全与微隔离-小佑表格数据
      xyList: [],
      // 弹出层标题
      title: '',
      // 提示输入框数据
      nameInputData: [],
      scopeData: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        cluster: null,
        level: null,
        createTime: null,
        status: null,
        hostIp: null,
        deptName: null,
        systemName: null,
        dispatchStatus: void 0,
        riskOperatorGroupId: void 0
      },
      daterange: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tenantId: [{ required: true, message: '租户id不能为空', trigger: 'blur' }],
        tenantName: [{ required: true, message: '租户名称不能为空', trigger: 'blur' }],
        containerId: [{ required: true, message: '容器id不能为空', trigger: 'blur' }],
        dstIp: [{ required: true, message: '目的ip不能为空', trigger: 'blur' }],
        srcIp: [{ required: true, message: '源ip不能为空', trigger: 'blur' }],
        createTime: [{ required: true, message: '创建时间不能为空', trigger: 'blur' }],
        updateTime: [{ required: true, message: '更新时间不能为空', trigger: 'blur' }]
      },

      repeatNumData: {
        repeatIds: null
      },

      analyse: {
        id: null,
        url: '/risk/xy/',
        type: 'xy',
        formLabel: formLabel
      },
      detail: {
        data: {},
        formLabel: [
          {
            label: '报警类型名称',
            field: 'name',
            expanded: true
          },
          {
            label: '报警类型描述',
            field: 'description',
            expanded: true
          },
          {
            label: '集群名称',
            field: 'cluster',
            expanded: true
          },
          {
            label: '容器名称',
            field: 'containerName',
            expanded: true
          },
          {
            label: '命名空间名称',
            field: 'namespaceName',
            expanded: true
          },
          {
            label: '主机名称',
            field: 'hostName',
            expanded: true
          },
          {
            label: '镜像仓库名',
            field: 'registryName',
            expanded: true
          },
          {
            label: '节点IP',
            field: 'hostIp',
            expanded: true
          },
          {
            label: '进程命令行',
            field: 'procCmd',
            row: 24,
            expanded: true,
            textarea: false
          },
          {
            label: '风险等级',
            field: 'level',
            type: 'risk_level',
            expanded: true
          },
          {
            label: '重复次数',
            field: 'repeatNum',
            expanded: true
          },
          {
            label: '目的IP',
            field: 'dstIp',
            expanded: true
          },
          {
            label: '源IP',
            field: 'srcIp',
            expanded: true
          },
          {
            label: '源端口',
            field: 'srcPort',
            expanded: true
          },
          {
            label: '文件报警的时间',
            field: 'fileCreateTime',
            type: 'time',
            expanded: true
          },
          {
            label: '修改文件时间',
            field: 'fileModifyTime',
            type: 'time',
            expanded: true
          },
          {
            label: '文件路径',
            field: 'filePath',
            expanded: true
          },
          {
            label: '用户名',
            field: 'userName',
            expanded: true
          },
          {
            label: '是否封锁',
            field: 'isBlock',
            expanded: true
          },
          {
            label: '报警信息ID',
            field: 'warnInfoId',
            expanded: true
          },
          {
            label: '报警类型ID',
            field: 'warnType',
            expanded: true
          },
          {
            label: '容器ID',
            field: 'containerId',
            expanded: true
          },
          {
            label: '集群ID',
            field: 'clusterId',
            expanded: true
          },
          {
            label: '进程ID',
            field: 'pid',
            expanded: true
          },
          {
            label: '数据推送来源',
            field: 'syslogTag',
            type: 'syslogTag',
            expanded: true
          },
          {
            label: 'APP服务ID',
            field: 'appServiceId',
            expanded: true
          },
          {
            label: '邮件发送次数',
            field: 'sendNum',
            expanded: true
          },
          {
            label: '处置状态',
            field: 'status',
            type: 'risk_status',
            expanded: false
          },

          {
            label: '租户名称',
            field: 'tenantName'
          },
          {
            label: '处理时间',
            field: 'deletedAt',
            type: 'time'
          },
          {
            label: '主机ID',
            field: 'hostId'
          },
          {
            label: '镜像名称',
            field: 'imageName'
          },
          {
            label: '进程名',
            field: 'procName'
          },
          {
            label: '目的端口',
            field: 'dstPort'
          },
          {
            label: '镜像规则所属IDS',
            field: 'imageSecRuleId'
          },
          {
            label: '报警触发次数',
            field: 'triggerCount'
          },
          {
            label: '唯一值',
            field: 'unionStr'
          },
          {
            label: '容器规则所属IDS',
            field: 'containerSecRuleId'
          },
          {
            label: '处理状态',
            field: 'warnInfoStatus'
          },
          {
            label: '备注',
            field: 'remark'
          },
          {
            label: '文件MD5值',
            field: 'fileMd5'
          },
          {
            label: '文件SHA256值',
            field: 'fileSha256'
          },
          {
            label: '父进程名称',
            field: 'pprocName'
          },
          {
            label: '下载路径',
            field: 'downloadPath'
          },
          {
            label: 'SRC命名空间',
            field: 'srcNamespace'
          },
          {
            label: 'DST命名空间',
            field: 'dstNamespace'
          },
          {
            label: 'SRC类型',
            field: 'srcType'
          },
          {
            label: 'DST类型',
            field: 'dstType'
          },
          {
            label: '用户组集合',
            field: 'userGroups'
          },
          {
            label: '用户代理商',
            field: 'userAgent'
          },
          {
            label: '注释',
            field: 'annotations'
          },
          {
            label: '请求的URL',
            field: 'requestUri'
          },
          {
            label: '响应状态',
            field: 'responseStatus'
          },
          {
            label: '方法',
            field: 'method'
          },
          {
            label: '服务名称',
            field: 'serviceName'
          },
          {
            label: '参数',
            field: 'args'
          },
          {
            label: '政策名称',
            field: 'policyName'
          },

          {
            label: '政策ID',
            field: 'policyId'
          },
          {
            label: '是否阻塞',
            field: 'packet'
          },
          {
            label: '服务端口',
            field: 'servicePort'
          },
          {
            label: '标签',
            field: 'tag'
          },
          {
            label: '标签-评论',
            field: 'tagComment'
          },
          {
            label: '处置人名称',
            field: 'disposeName'
          },
          {
            label: '备注',
            field: 'remarks'
          }
          // {
          //   label: '租户ID',
          //   field: 'tenantId',
          // },
          // {
          //   label: '处置人ID',
          //   field: 'disposeId',
          // },
          // {
          //   label: '重复告警IDS',
          //   field: 'repeatIds',
          // },

          // {
          //   label: '创建时间',
          //   field: 'createdAt',
          //   type: 'time',
          // },
          // {
          //   label: '更新时间',
          //   field: 'updatedAt',
          //   type: 'time',
          // },
          // {
          //   label: '集群名称',
          //   field: 'clusterName',
          // },
          // {
          //   label: '创建人',
          //   field: 'createBy',
          // },
          // {
          //   label: '创建时间',
          //   field: 'createTime',
          // },
          // {
          //   label: '更新人',
          //   field: 'updateBy',
          // },
          // {
          //   label: '更新时间',
          //   field: 'updateTime',
          // },
        ],
        isShowOper: true
      },

      mibbleReassignmentData: {
        riskType: '4',
        riskIds: [],
        userIds: []
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    statisticsData(val) {
      this.queryParams = { ...this.queryParams, ...val }
      this.getList()
    }
  },
  created() {
    this.queryParams = { ...this.queryParams, ...this.$props.statisticsData }
    this.getList()
  },
  methods: {
    // 添加一键分配方法
    async onOneClickAssignClick(ids) {
      if (ids.length === 0) {
        this.$message.warning('请选择需要一键分配的数据')
        return
      }

      try {
        await this.$confirm('确定要执行一键分配吗?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return
      }

      // 获取组件的查询条件参数
      const queryObj = {
        ...this.queryParams
      }

      const res = await riskDisposeOneClickAssign({
        id: ids.join(','),
        riskType: '6',
        queryObj
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.getList()
      }
    },
    getContainerSafeType(row) {
      if (['guanan'].includes(row.syslogTag)) {
        return 2
      }

      return 1
    },
    /** 查询容器安全与微隔离-小佑列表 */
    getList() {
      this.loading = true
      listXy(this.queryParams).then((response) => {
        this.xyList = response.rows
        this.total = response.total
        this.loading = false
      })

      this.$emit('query-change', this.queryParams)
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        tenantId: null,
        tenantName: null,
        status: null,
        clusterName: null,
        name: null,
        description: null,
        warnInfoId: null,
        warnType: null,
        level: null,
        createdAt: null,
        updatedAt: null,
        deletedAt: null,
        hostName: null,
        hostId: null,
        containerId: null,
        containerName: null,
        imageName: null,
        registryName: null,
        userName: null,
        procName: null,
        procCmd: null,
        pprocCmd: null,
        filePath: null,
        dstIp: null,
        dstPort: null,
        srcPort: null,
        sendNum: null,
        srcIp: null,
        clusterId: null,
        imageSecRuleId: null,
        triggerCount: null,
        unionStr: null,
        containerSecRuleId: null,
        warnInfoStatus: null,
        remark: null,
        namespaceName: null,
        fileCreateTime: null,
        fileModifyTime: null,
        fileMd5: null,
        fileSha256: null,
        pid: null,
        pprocName: null,
        hostIp: null,
        downloadPath: null,
        srcNamespace: null,
        dstNamespace: null,
        srcType: null,
        dstType: null,
        srcObject: null,
        dstObject: null,
        protocol: null,
        detectEngine: null,
        sourceIps: null,
        verb: null,
        requestObject: null,
        userGroups: null,
        userAgent: null,
        annotations: null,
        requestUri: null,
        responseStatus: null,
        method: null,
        serviceName: null,
        args: null,
        policyName: null,
        appServiceId: null,
        policyId: null,
        packet: null,
        isBlock: null,
        servicePort: null,
        tag: null,
        tagComment: null,
        disposeId: null,
        disposeName: null,
        remarks: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      }
      this.resetForm('form')
    },
    // 失去焦点触发
    onBlurName() {
      setTimeout(() => {
        this.isName = false
      }, 500)
    },
    // 输入框改变
    onChangeNameInput(val) {
      this.isName = true
      request({
        url: `/risk/warn/hint/search`,
        method: 'get',
        params: {
          riskType: 0,
          riskWarnType: 20001,
          riskContainerSafetyType: 1,
          field: 'name',
          value: val
        }
      }).then((response) => {
        if (!response) {
          this.nameInputData = []
        } else {
          this.nameInputData = response.data
        }
      })
    },
    onNameInput(val) {
      this.isName = false
      this.queryParams.name = val
    },
    // 失去焦点触发
    onBlurScope() {
      setTimeout(() => {
        this.isScope = false
      }, 500)
    },
    // 集群名称输入框改变
    onChangeScope(val) {
      this.isScope = true
      request({
        url: `/risk/warn/hint/search`,
        method: 'get',
        params: {
          riskType: 0,
          riskWarnType: 20001,
          riskContainerSafetyType: 1,
          field: 'cluster',
          value: val
        }
      }).then((response) => {
        if (!response) {
          this.scopeData = []
        } else {
          this.scopeData = response.data
        }
      })
    },
    onScopeMsg(val) {
      this.isScope = false
      this.queryParams.cluster = val
    },

    inputHandler() {
      this.queryParams.hostIp = this.queryParams.hostIp.replace(/，/, ',')
    },

    /** 搜索按钮操作 */
    handleQuery() {
      if (this.queryParams.startIp && !this.queryParams.endIp) {
        this.$message.warning('请输入结束ip')
        return false
      }

      this.queryParams.pageNum = 1
      this.getList()
    },
    // 获取开始ip
    getStartIp(val) {
      this.queryParams.startIp = val
    },
    // 获取结束ip
    getEndIp(val) {
      this.queryParams.endIp = val
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.startIp = ''
      this.queryParams.endIp = ''
      this.daterange = []
      this.onChangeDaterange()
      this.handleQuery()
      this.$refs.ipRef.reset()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.repeatIds = selection.map((item) => item.repeatIds)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加容器安全与微隔离-小佑'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getXy(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改容器安全与微隔离-小佑'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateXy(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addXy(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.repeatIds.join(',').split(',')
      // this.$modal
      //   .confirm('是否确认删除容器安全与微隔离-小佑编号为"' + ids + '"的数据项？')
      //   .then(function () {
      //     return delXy(ids);
      //   })
      //   .then(() => {
      //     this.getList();
      //     this.$modal.msgSuccess('删除成功');
      //   })
      //   .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      if (this.total > 200000) {
        this.$modal.msgWarning('抱歉，导出数据量过大，请选择筛选条件后再进行导出')
        return false
      }

      const submitAuery = JSON.parse(JSON.stringify(this.queryParams))
      delete submitAuery.pageNum
      delete submitAuery.pageSize

      this.download(
        '/risk/xy/group/export',
        {
          ...submitAuery
        },
        `导出${this.$route.meta.title}_${new Date().getTime()}.xlsx`
      )
    },
    handleStatsExport() {
      if (!this.daterange.length) {
        this.$message.warning('请选择创建时间')
        return false
      }

      this.download(
        '/risk/xy/group/exportByWarnType',
        {
          ...this.queryParams
        },
        `导出${this.$route.meta.title}_统计报表_${new Date().getTime()}.xlsx`
      )
    },
    onOpenAnalyse(row) {
      this.analyse.id = row.id
      this.analyse.search = {
        containerId: row.containerId,
        dstIp: row.dstIp,
        srcIp: row.srcIp
      }
      this.$refs.analyse.show()
    },
    onOper(row) {
      this.$refs.operDockfile.show(row ? String(row.id).split(',') : this.ids, {
        riskContainerSafetyType: this.getContainerSafeType(row)
      })
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case 'mibbleSendBack':
          this.onMibbleSendBack(row)
          break
        case 'mibbleReassignment':
          this.onMibbleReassignment(row)
          break
        case 'analyse':
          this.onOpenAnalyse(row)
          break
        case 'markEnd':
          this.onMark(row)
          break
        case 'joinWhiteList':
          this.$refs.whitelistDialogRef.open({
            id: row.id,
            containerSafeType: this.getContainerSafeType(row)
          })
          break
        default:
          break
      }
    },
    onDetail(row) {
      // this.detail.data = row
      // this.$refs.detail.show()
      this.$refs.infoDialogRef.open(row)
    },
    checkSelectable(row) {
      return row.status != 2
    },
    onOkOper() {
      this.getList()
      this.$emit('updateMiddleHandleStatistics')
    },
    onRepeatNum(row) {
      this.repeatNumData.repeatIds = row.repeatIds
      this.$refs.repeatNumRef.show()
    },
    onChangeDaterange() {
      if (this.daterange && this.daterange.length > 0) {
        this.queryParams.startTime = this.daterange[0]
        this.queryParams.endTime = this.daterange[1]
      } else {
        delete this.queryParams.startTime
        delete this.queryParams.endTime
      }
    },
    onMibbleReassignment(row) {
      this.mibbleReassignmentData.riskIds = row.id ? [row.id] : this.ids
      this.$refs.mibbleReassignmentRef.show()
    },
    onMibbleSendBack(row) {
      request({
        url: '/risk/warn/mibbleSendBack',
        method: 'post',
        data: {
          riskType: '4',
          riskIds: row.id ? [row.id] : this.ids,
          userId: this.userInfo.userId
        }
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        if (response.code === 200) {
          this.getList()
        }
      })
    },
    unknownFormat(row, column, cellValue) {
      return cellValue == null || cellValue == '' ? '未知' : cellValue
    },
    onMark(row) {
      this.$refs.markDialog.show({ fallback: true, ids: [row.id], status: row.status, riskContainerSafetyType: this.getContainerSafeType(row) })
    },
    onBatchMark(type) {
      const params = {}

      if (type === 'select') {
        Object.assign(params, { ids: this.ids })
      } else {
        Object.assign(params, { ...this.queryParams })
      }

      this.$refs.markDialog.show(params)
    }
  }
}
</script>

<style lang="scss" scoped>
  .prompt {
    position: relative;
    .prompt-box {
      position: absolute;
      left: -28px;
      top: 40px;
    }
    .prompt-box-content {
      position: relative;
      width: 200px;
      height: auto;
      z-index: 100;
      border: solid 1px #dfe4ed;
      border-radius: 4px;
      background-color: #ffffff;
      padding: 8px;
      white-space: nowrap;
      overflow-x: scroll;
    }
    .ovflow-y {
      height: 260px;
      overflow-y: scroll;
    }
    .prompt-box-sanjiao {
      position: absolute;
      top: -9px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 101;
    }
    .sanjiao {
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 0 10px 10px;
      border-color: transparent transparent #dfe4ed;
      position: relative;
    }
    .sanjiao::after {
      content: '';
      border-style: solid;
      border-width: 0 9px 9px;
      border-color: transparent transparent #fff;
      position: absolute;
      top: 1px;
      left: -9px;
    }
    .input-msg-item {
      padding-bottom: 6px;
      // border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
    }
  }
</style>
