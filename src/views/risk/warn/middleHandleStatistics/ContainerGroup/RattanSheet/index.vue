<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main h-full" v-on="$listeners">
    <template #before> </template>

    <template #toolbar:after="{ selected, selectionIds, selection }">
      <el-button
        v-hasPermi="['risk:warn:oneClickAssign']"
        type="success"
        :disabled="!selected"
        @click="onOneClickAssignClick(selectionIds)"
      ><i class="el-icon-thumb" />一键分配</el-button>

      <el-button
        v-hasPermi="['risk:warn:qt:dispose']"
        type="primary"
        :disabled="!selected"
        @click="onDisposeClick(selectionIds, selection)"
      ><IconHandyman class="" />处置</el-button>

      <el-button
        v-if="$checkPermi(['risk:turnoverRecord:qt:goBackRiskData'])"
        type="warning"
        :disabled="!selected"
        @click="onBackClick(selectionIds, selection)"
      ><IconUndo class="el-icon" />退回</el-button>

      <el-button
        v-if="$checkPermi(['risk:turnoverRecord:qt:transferRiskData'])"
        type="warning"
        :disabled="!selected"
        @click="handleAssign(selectionIds, { title: '转派' })"
      ><IconForward class="" />转派</el-button>

      <!-- <el-button
        v-if="$checkPermi(['risk:qt:group:markers'])"
        type="success"
        :disabled="!selected"
        @click="onMarkClick(selectionIds)"
      >
        <IconCheck class="icon" />标记
      </el-button> -->

      <el-button
        v-if="$checkPermi(['risk:middle:qt:assign'])"
        type="warning"
        icon="iconfont icon-piliangfenpei1"
        :disabled="!selected"
        @click="handleAssign(selectionIds)"
      >分配</el-button>
    </template>

    <template #search:deptName:simple="{ model }">
      <CommonDepartmentSelect
        v-model="model.deptName"
        placeholder="请输入"
        clearable
        return-name
        @change="() => (model.systemName = void 0)"
      />
    </template>

    <template #search:systemName:simple="{ model }">
      <CommonSystemSelect
        v-model="model.systemName"
        placeholder="请输入"
        return-name
        clearable
        :params="{
          deptName: model.deptName,
        }"
      />
    </template>

    <template #search:riskOperatorGroupId:simple="{ model }">
      <DisposeGroupSelect
        v-model="model.riskOperatorGroupId"
        placeholder="选择处置分组"
        clearable
        :params="{
          groupKey: 'basic_type_middle_dim',
          disposeType,
        }"
      />
    </template>

    <template #table:action:after="{ row }">
      <el-dropdown :key="row.id" v-check-dropdown-items>
        <el-button v-hasPermi="['risk:warn:oneClickAssign']" type="text" size="mini" @click="onOneClickAssignClick([row.id])">一键分配</el-button>

        <el-button type="text" size="mini">更多</el-button>

        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-if="
                $checkPermi(['risk:turnoverRecord:qt:goBackRiskData']) &&
                  !['2'].includes(String(row.status))
              "
              @click.native="onBackClick([row.id], row)"
            >退回</el-dropdown-item>
            <el-dropdown-item
              v-if="
                $checkPermi(['risk:turnoverRecord:qt:transferRiskData']) &&
                  !['2'].includes(String(row.status))
              "
              @click.native="handleAssign([row.id], { title: '转派' })"
            >转派</el-dropdown-item>
            <el-dropdown-item
              v-if="$checkPermi(['risk:qt:relevance:list'])"
              @click.native="onAnalyzeClick(row)"
            >分析</el-dropdown-item>
            <!-- <el-dropdown-item v-if="$checkPermi(['risk:qt:group:markers'])" @click.native="onMarkClick([row.id], row)">标记</el-dropdown-item> -->
            <!-- <el-dropdown-item v-if="$checkPermi(['risk:qt:group:whiteList'])" @click.native="onWhiteClick([row.id], row)">加入白名单</el-dropdown-item> -->
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>

    <template #after>
      <AssignDialog ref="assignDialogRef" />
      <TurnDialog ref="turnDialogRef" />
      <MarkDialog ref="markDialogRef" />
      <DisposeDialog ref="disposeDialogRef" />
      <WhiteDialog ref="whiteDialogRef" />
      <AnalyzeDialog ref="analyzeDialogRef" />
      <ReturnDialog ref="returnDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import AssignDialog from '@/views/risk/basic/components/AssignDialog/index.vue'
import TurnDialog from '@/views/risk/warn/middleHandleStatistics/ContainerGroup/TurnDialog/index.vue'
import MarkDialog from '@/views/risk/warn/middleHandleStatistics/ContainerGroup/MarkDialog/index.vue'
import DisposeDialog from '@/views/risk/basic/components/DisposeDialog/index.vue'
import WhiteDialog from '@/views/risk/warn/middleHandleStatistics/ContainerGroup/WhiteDialog/index.vue'
import AnalyzeDialog from './AnalyzeDialog/index.vue'
import ReturnDialog from '@/views/risk/basic/components/ReturnDialog/index.vue'
import { riskDisposeOneClickAssign } from '@/api/risk/basic/index.js'

export default {
  dicts: [
    'allocation_status',
    'container_safety_one_dispose_status',
    'dispose_status_container_qt',
    'qt_severity'
  ],
  components: {
    AssignDialog,
    TurnDialog,
    MarkDialog,
    DisposeDialog,
    WhiteDialog,
    AnalyzeDialog,
    ReturnDialog
  },
  data() {
    return {
      riskType: '21',
      scopeRiskType: '5',
      disposeType: '容器安全',
      riskContainerSafetyType: '3'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '3期-青藤云',

        lazy: false,

        api: {
          // add: (params) => addController({ ...params }),
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          list: async(params) => request({ url: '/risk/container/qt/group/list', method: 'get', params }),
          info: async(id) => request({ url: `/risk/container/qt/${id}`, method: 'get' }),
          export: '/risk/container/qt/group/export',
          import: '',
          template: ''
        },

        flowProps: {
          preset: 'disposal',
          params: {
            riskType: this.riskType
          }
        },

        hiddenActions: {
          export: !this.$checkPermi(['risk:middle:qt:export'])
        },

        tableProps: {
          selection: 'multiple',
          height: '100%'
        },

        infoProps: {
          title: true
        },

        model: {
          detectionType: { label: '告警类型', search: {}, table: { width: 150, align: 'left' }, form: {}},
          detectionName: { label: '告警名称', search: {}, table: { width: 150 }, form: {}},
          detectionDescription: {
            label: '告警描述',
            search: {},
            table: { width: 150 },
            form: {}
          },
          deptName: { label: '部门名称', search: {}, table: { width: 150 }, form: {}},
          systemName: { label: '业务系统', search: {}, table: { width: 150 }, form: {}},
          severity: { label: '严重等级', search: {}, table: {}, form: {}, type: 'select', options: this.dict.type.qt_severity },
          agentIp: { label: '主机 IP', search: {}, table: { width: 150 }, form: {}},
          agentName: { label: '主机名', search: {}, table: { width: 150 }, form: {}},
          containerId: { label: '容器 ID', search: {}, table: { width: 150 }, form: {}},
          containerName: { label: '容器名', search: {}, table: { width: 150 }, form: {}},
          status: {
            label: '处置状态',
            type: 'select',
            search: {},
            table: { },
            form: {},
            options: this.dict.type.dispose_status_container_qt,
            group: 'flow'
          },
          dispatchStatus: {
            type: 'select',
            label: '分配状态',
            search: {},
            table: { },
            form: {},
            options: this.dict.type.allocation_status,
            group: 'flow'
          },
          riskOperatorGroupName: {
            label: '处置分组',
            search: {
              hidden: true
            },
            table: { width: 150 },
            form: {
              hidden: true
            },
            group: 'flow'
          },
          riskOperatorGroupId: {
            label: '处置分组',
            search: {},
            table: {
              hidden: true
            },
            form: {}
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-time-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          },

          datatime: {
            label: '发生时间',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          hostName: {
            label: '主机名',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          internalIp: {
            label: '内网 IP',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          externalIp: {
            label: '外网 IP',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          hostTag: {
            label: '主机标签',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          hostMemo: {
            label: '主机备注',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          groupName: {
            label: '业务组',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          groupId: {
            label: '主机业务组 ID',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          containerType: {
            label: '容器类型',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          imageId: {
            label: '镜像 ID',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          imageName: {
            label: '镜像名',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          podName: {
            label: '容器所属 Pod',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          namespace: {
            label: '容器所属 Namespace',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          clusterId: {
            label: '集群 ID',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          clusterName: {
            label: '集群名',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },

          detectionId: {
            label: '告警 ID',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          detectionMethod: {
            label: '检测方式',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          score: {
            label: '告警分值',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          actionDesc: {
            label: '行为描述',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          tacticsTechniques: {
            label: '技术与战术',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          handleSuggestion: {
            label: '处理建议',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          urlDetection: {
            label: '告警链接',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },

          detailProcessFile: {
            label: '进程文件信息',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          detailProcessTree: {
            label: '进程树信息',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          detailFpath: {
            label: '文件路径',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          detailFname: {
            label: '文件名称',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          detailPid: {
            label: '进程 PID',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          detailPname: {
            label: '进程名',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          detailIntelligenceName: {
            label: '威胁情报告警信息',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          },
          detailDstIp: {
            label: '进程访问目标 IP',
            search: { hidden: true },
            table: { hidden: true },
            form: {},
            group: 'detail'
          }
        }
      }

      return value
    }
  },
  methods: {
    async onOneClickAssignClick(ids) {
      if (ids.length === 0) {
        this.$message.warning('请选择需要一键分配的数据')
        return
      }

      try {
        await this.$confirm('确定要执行一键分配吗?', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return
      }

      // 获取组件的查询条件参数
      let queryObj = {}
      if (this.$refs.sheetRef) {
        queryObj = { ...this.$refs.sheetRef.listParameter() }
      }

      const res = await riskDisposeOneClickAssign({
        id: ids.join(','),
        riskType: '21',
        queryObj
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    },
    handleAssign(ids, args = {}) {
      const params = {
        ids,
        riskType: this.riskType,
        disposeType: this.disposeType,
        groupKey: 'basic_type_middle_dim'
      }

      this.$refs.assignDialogRef.open({
        ...args,
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },

    async onBackClick(ids, selection) {
      const params = Array.isArray(ids)
        ? {
          ids,
          selection: selection
        }
        : {
          id: ids[0]
        }

      Object.assign(params, {
        riskType: this.riskType
        // riskCategory: ''
      })

      this.$refs.returnDialogRef.open({
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onTurnClick(ids) {
      this.$refs.turnDialogRef.open({
        params: {
          riskType: this.scopeRiskType,
          riskIds: ids
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onMarkClick(ids) {
      this.$refs.markDialogRef.open({
        params: {
          riskContainerSafetyType: this.riskContainerSafetyType,
          ids
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onDisposeClick(ids, selection) {
      let params = {}

      if (Array.isArray(ids)) {
        params = {
          ids,
          selection
        }
      } else {
        params = {
          id: ids[0]
        }
      }

      Object.assign(params, {
        riskType: this.riskType
        // riskCategory: ''
      })

      this.$refs.disposeDialogRef.open({
        params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onWhiteClick(ids) {
      this.$refs.whiteDialogRef.open({
        params: {
          riskContainerSafetyType: this.riskContainerSafetyType,
          ids
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    onAnalyzeClick(row) {
      this.$refs.analyzeDialogRef.open({
        sheetProps: this.sheetProps,
        params: {
          id: row.id
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    }
  }
}
</script>

<style></style>
