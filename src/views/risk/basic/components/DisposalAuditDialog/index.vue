<template>
  <el-dialog title="处置审核" :visible.sync="dialogMixin.visible" custom-class="el-dialog--mini" append-to-body width="800px" @closed="onClosed">
    <div v-if="dialogMixin.lazyVisible" class="">
      <el-form ref="formRef" :model="formInfo" :rules="formRules" label-width="120px">
        <el-form-item label="审核结果" prop="riskAuditStatus">
          <el-radio-group v-model="formInfo.riskAuditStatus">
            <el-radio v-for="(item,index) of filteredReviewStatus" :key="item.value" :label="item.value"> {{ item.label }} </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="riskAuditResult">
          <el-input v-model="formInfo.riskAuditResult" type="textarea" placeholder="请输入审核意见" />
        </el-form-item>
        <el-form-item label="审核附件">
          <file-upload v-model="fileList" :file-size="50" :limit="5" @change="handleFileChange" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'
import FileUpload from '@/components/FileUpload'
import { riskDisposeDisposalAudit } from '@/api/risk/basic/index.js'

export default {
  components: {
    FileUpload
  },
  dicts: ['dispose_review_status'],
  mixins: [dialogMixin()],
  data() {
    return {
      formInfo: {
        riskAuditStatus: '2', // 默认审核通过
        riskAuditResult: '',
        riskAuditFileUrl: '',
        riskAuditFileName: '',
        riskAuditFileSize: ''
      },
      fileList: [],
      formRules: {
        riskAuditStatus: [{ required: true, message: '请选择审核结果', trigger: 'change' }]
      }
    }
  },

  computed: {
    filteredReviewStatus() {
      return this.dict.type.dispose_review_status.filter(item => ['2', '3'].includes(item.dictValue || item.value))
    }
  },

  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
      // 确保在对话框打开时设置默认值
      this.$nextTick(() => {
        this.formInfo.riskAuditStatus = '2' // 确保字符串类型
      })
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.formInfo = this.$options.data().formInfo
      this.fileList = []
      this.dialogMixin.reset()
    },
    handleFileChange(fileList) {
      if (fileList && fileList.length > 0) {
        const fileUrls = fileList.map(file => file.url).join(',')
        const fileNames = fileList.map(file => file.name).join(',')
        const fileSizes = fileList.map(file => file.size).join(',')
        this.formInfo.riskAuditFileUrl = fileUrls
        this.formInfo.riskAuditFileName = fileNames
        this.formInfo.riskAuditFileSize = fileSizes
      } else {
        this.formInfo.riskAuditFileUrl = ''
        this.formInfo.riskAuditFileName = ''
        this.formInfo.riskAuditFileSize = ''
      }
    },
    async submit() {
      await this.$refs.formRef.validate()

      const ids = this.dialogMixin.params.id
        ? [this.dialogMixin.params.id]
        : this.dialogMixin.params.ids

      const params = {
        riskBasicType: this.dialogMixin.params.riskType,
        ...this.formInfo,
        ids
      }

      this.dialogMixin.loading = true

      try {
        const res = await riskDisposeDisposalAudit(params)

        if (res.code === 200) {
          this.$message.success(res.msg)
          this.dialogMixin.success()
          this.close()
        }
      } catch (error) {
        console.warn(error?.message || error)
      }

      this.dialogMixin.loading = false
    }
  }
}
</script>

<style></style>
