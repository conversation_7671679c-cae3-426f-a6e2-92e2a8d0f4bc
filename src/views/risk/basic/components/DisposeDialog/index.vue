<template>
  <el-dialog
    title="处置"
    :visible.sync="dialogMixin.visible"
    append-to-body
    :custom-class="!recordList.length ? 'el-dialog--mini' : ''"
    width="800px"
    @closed="onClosed"
  >
    <div v-if="dialogMixin.lazyVisible" class="flex">
      <div class="flex-1 w-0">
        <el-form ref="formRef" :model="formInfo" :rules="formRules" label-width="120px">
          <el-form-item label="处置结果" prop="disposeResult">
            <el-select v-model="formInfo.disposeResult" placeholder="请选择处置结果" clearable>
              <el-option
                v-for="(dict, index) of activeDispose.disposeResultDict"
                :key="index"
                :label="dict.label"
                :value="dict.value"
                :disabled="activeDispose.disposeResultDisabled(dict)"
              />
            </el-select>
            <el-tooltip v-if="activeDispose.disposeResultTips" effect="dark" placement="right">
              <el-link :underline="false" type="primary" icon="el-icon-question" class="ml-2" />

              <template #content>
                <div class="w-96" v-html="activeDispose.disposeResultTips" />
              </template>
            </el-tooltip>
          </el-form-item>

          <el-form-item label="处置方案" prop="remark">
            <el-input v-model="formInfo.remark" type="textarea" placeholder="请输入处置方案" />
          </el-form-item>

          <el-form-item
            v-if="
              pluginOptions.accessory &&
                pluginOptions.accessory.show &&
                pluginOptions.accessory.show(formInfo.disposeResult)
            "
            label="上传申请附件"
            v-bind="{
              ...(pluginOptions.accessory &&
                pluginOptions.accessory.required &&
                pluginOptions.accessory.required(formInfo.disposeResult)
                ? {
                  prop: 'accessory',
                }
                : {}),
            }"
          >
            <EleUpload v-model="formInfo.accessory" />
          </el-form-item>
        </el-form>
      </div>
      <div v-if="recordList.length" class="flex-1 w-0">
        <div class="">
          <div class="page-detail-title sa-flex sa-row-center sa-m-t-20 sa-m-b-20">流转记录</div>
          <div class="sa-flex sa-row-center">
            <el-timeline class="el-timeline--primary">
              <el-timeline-item
                v-for="(item, index) in recordList"
                :key="index"
                :timestamp="item.createTime"
              >
                {{ item.opUserName }}
                <span v-if="item.sendGroupName">（{{ item.sendGroupName }}）</span>
                {{ item.opDesc }}
                {{ item.receiveGroupName }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'

import request from '@/utils/request.js'

import { riskDispose } from '@/api/risk/basic/index.js'

import {
  assignType,
  mirrorType,
  mirrorCheckType,
  auditType,
  middleRattanType,
  middleAsiaType,
  middleSourceCodeType,
  middleAppTestType
} from '@/dicts/basic/index.js'

import EleUpload from '@/plugins/element-extends/components/EleField/Upload/index.vue'

import { normalizeOptions } from '@/plugins/element-extends/helper.js'

export default {
  dicts: [],
  mixins: [dialogMixin()],
  components: {
    EleUpload
  },
  data() {
    return {
      formInfo: {
        disposeResult: void 0,
        remark: void 0,
        accessory: void 0
      },

      formRules: {
        disposeResult: [{ required: true, message: '该选项不能为空', trigger: 'blur' }],
        accessory: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
      },

      recordList: [],
    }
  },
  computed: {
    disposeResultMap() {
      return {
        [assignType]: ['已知'].includes(this.dialogMixin.params.discoverType)
          ? 'dispose_result_known_zc'
          : 'dispose_result_unknown_zc',
        [mirrorCheckType]: 'dispose_result_jxpc',
        [mirrorType]: 'dispose_result_jxpc',
        [auditType]: 'dispose_result_aqsj',
        [middleRattanType]: 'dispose_result_container_qt',
        [middleAsiaType]: 'dispose_result_container_yx',
        [middleSourceCodeType]: 'dispose_result_source_code',
        [middleAppTestType]: 'dispose_result_app_detection',
        6: 'container_safety_one_dispose_status',
        18: 'container_safety_one_dispose_status',
        8: 'defect_status',
        20: 'pii_status',
        19: 'attack_status',
      }
    },
    disposeResultKey() {
      return this.disposeResultMap[this.dialogMixin.params.riskType]
    },
    disposeResultDict() {
      return this.dict.type[this.disposeResultKey] || []
    },
    disposeModel() {
      const disposeResultDict = this.disposeResultDict

      return {
        [assignType]: {
          disposeResultDict,
          disposeResultDisabled: (dict) => [].includes(String(dict.value)),
          disposeResultTips: `<div class="leading-5">对于已核查，核查后资产正常。<br />
              对于已下线，审核后会更新此主机资产状态为已下线。<br />
              对于已补录，发现未知资产的处置场景，此种情况是已经完成在磐基、本平台内主机资产的补录。<br />
              对于暂不处理，发现未知资产在磐基、本平台中中缺失，但又无法查到此主机的具体情况 <br />
              对于新增资产，属于已知资产但在磐基、本平台内找不到此主机，在待核查列表出进行补录主机资产，审核后会自动同步到主机资产中。
            </div>`
        },
        [mirrorCheckType]: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        [mirrorType]: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        [auditType]: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        [middleRattanType]: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        [middleAsiaType]: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        [middleSourceCodeType]: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        [middleAppTestType]: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        6: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        18: {
          disposeResultDict: disposeResultDict.filter(
            (item) => !['10'].includes(String(item.value))
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        8: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        20: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
        19: {
          disposeResultDict: disposeResultDict.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        },
      }
    },
    activeDispose() {
      return this.disposeModel[this.dialogMixin.params.riskType]
    },
    pluginOptions() {
      return normalizeOptions(this.dialogMixin.options.plugins)
    }
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)

      if (!this.dialogMixin.params.ids) {
        this.getRecordList()
      }

      if (this.disposeResultKey) {
        this.dict.init([this.disposeResultKey])
      }
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.formInfo = this.$options.data().formInfo
      this.dialogMixin.reset()
    },
    async submit() {
      await this.$refs.formRef.validate()

      const ids = this.dialogMixin.params.id
        ? [this.dialogMixin.params.id]
        : this.dialogMixin.params.ids

      const params = {
        ...this.formInfo,
        riskCategory: this.dialogMixin.params.riskCategory,
        riskType: this.dialogMixin.params.riskType,
        ids
      }

      if (this.dialogMixin.params.taskId) {
        delete params.ids
        params.taskId = this.dialogMixin.params.taskId
      }

      if (params.accessory?.length) {
        params.accessoryUrl = params.accessory[0].url
        params.accessoryName = params.accessory[0].name
        delete params.accessory
      }

      this.dialogMixin.loading = true

      try {
        const res = await riskDispose(params)

        if (res.code === 200) {
          this.$message.success(res.msg)
          this.dialogMixin.success()
          this.close()
        }
      } catch (error) {
        console.warn(error?.message || error)
      }

      this.dialogMixin.loading = false
    },

    async getRecordList() {
      try {
        const res = await request({
          url: '/risk/turnoverRecord/getRiskDataTurnoverRecord',
          method: 'post',
          data: {
            riskType: this.dialogMixin.params.riskType,
            id: this.dialogMixin.params.id
          }
        })

        this.recordList = res.data
      } catch (error) {
        console.warn(error?.message)
      }
    }
  }
}
</script>

<style></style>
