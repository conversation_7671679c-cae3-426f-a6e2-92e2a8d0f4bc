import request from '@/utils/request'

// 查询APP安全检测知识库列表
export function listAppLibrary(query) {
  // 确保查询参数中包含riskBasicType=112
  const params = { ...query, riskBasicType: '112' }
  return request({
    url: '/know/library/app/list',
    method: 'get',
    params
  })
}

// 查询APP安全检测知识库详细
export function getAppLibrary(id) {
  return request({
    url: '/know/library/app/' + id,
    method: 'get'
  })
}

// 新增APP安全检测知识库
export function addAppLibrary(data) {
  // 确保数据中包含riskBasicType=112
  const params = { ...data, riskBasicType: '112' }
  return request({
    url: '/know/library/app',
    method: 'post',
    data: params
  })
}

// 修改APP安全检测知识库
export function updateAppLibrary(data) {
  // 确保数据中包含riskBasicType=112
  const params = { ...data, riskBasicType: '112' }
  return request({
    url: '/know/library/app',
    method: 'put',
    data: params
  })
}

// 删除APP安全检测知识库
export function delAppLibrary(id) {
  return request({
    url: '/know/library/app/' + id,
    method: 'delete'
  })
}

// 导出APP安全检测知识库
export function exportAppLibrary(query) {
  // 确保查询参数中包含riskBasicType=112
  const params = { ...query, riskBasicType: '112' }
  return request({
    url: '/know/library/app/export',
    method: 'get',
    params
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/library/app/importTemplate',
    method: 'get'
  })
}

// 获取APP安全检测知识库统计数据
export function countAppLibrary(query) {
  // 确保查询参数中包含riskBasicType=112
  const params = { ...query, riskBasicType: '112' }
  return request({
    url: '/know/library/app/count',
    method: 'get',
    params
  })
}

// 合并APP安全检测知识库
export function mergeAppLibrary(query) {
  // 确保查询参数中包含riskBasicType=112
  const params = { ...query, riskBasicType: '112' }
  return request({
    url: '/know/library/app/merge',
    method: 'get',
    params
  })
} 