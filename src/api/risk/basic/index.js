import request from '@/utils/request'

export function riskReportTaskGetServiceSys(params = {}) {
  return request({
    url: '/risk/report/task/getServiceSys',
    method: 'get',
    params
  })
}

/** 系统|基线|弱口令|应用漏洞 */
export function riskWarnApproval(data = {}) {
  return request({
    url: '/risk/warn/approval',
    method: 'post',
    data
  })
}

/** 通用分配 */
export function riskDisposeAssign(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/assign',
    method: 'post',
    data
  })
}

/** 通用审核 */
export function riskDisposeAudit(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/audit',
    method: 'post',
    data
  })
}

/** 通用处置 */
export function riskDispose(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/dispose',
    method: 'post',
    data
  })
}

/** 通用退回 */
export function riskDisposeGoback(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/goback',
    method: 'post',
    data
  })
}

/** 一键分配 */
export function riskDisposeOneClickAssign(data = {}) {
  // 转换参数格式以匹配后端接口需求
  const params = { ...data }
  // 将riskType转换为riskBasicType
  if (params.riskType) {
    params.riskBasicType = params.riskType
    delete params.riskType
  }
  // 将id字符串转换为ids数组
  if (params.id) {
    params.ids = params.id.split(',').map(id => parseInt(id))
    delete params.id
  }

  // 添加queryObj参数，可以为空对象
  if (!params.queryObj) {
    params.queryObj = {}
  }

  return request({
    url: '/risk/dispose/oneClickAssign',
    method: 'post',
    data: params
  })
}

/** 处置审核 */
export function riskDisposeDisposalAudit(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/disposalAudit',
    method: 'post',
    data
  })
}
